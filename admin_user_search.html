<!DOCTYPE html>
<html lang="da">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Brugersøgning | Tilbudsjægeren</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .user-type-badge {
            @apply px-2 py-1 rounded-full text-xs font-medium;
        }
        .power-user { @apply bg-purple-100 text-purple-800; }
        .regular-user { @apply bg-green-100 text-green-800; }
        .casual-user { @apply bg-blue-100 text-blue-800; }
        .inactive-regular { @apply bg-yellow-100 text-yellow-800; }
        .inactive-casual { @apply bg-gray-100 text-gray-800; }
        .new-user { @apply bg-indigo-100 text-indigo-800; }
        
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">
                        <i class="fas fa-users mr-2 text-blue-600"></i>
                        Tilbudsjægeren Admin
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">Logget ind som admin</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i> Log ud
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">Brugersøgning</h2>
            <p class="mt-1 text-sm text-gray-600">
                Søg og analyser brugeraktivitet på tværs af platformen
            </p>
        </div>

        <!-- Search Filters -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Søgefiltre</h3>
            </div>
            <div class="px-6 py-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Search Term -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Søg efter brugernavn
                        </label>
                        <input type="text" id="searchTerm" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="Indtast brugernavn...">
                    </div>

                    <!-- User Type Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Brugertype
                        </label>
                        <select id="userType" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Alle typer</option>
                            <option value="active">Aktive brugere</option>
                            <option value="inactive">Inaktive brugere</option>
                            <option value="power_user">Power brugere</option>
                        </select>
                    </div>

                    <!-- Date From -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Fra dato
                        </label>
                        <input type="date" id="dateFrom" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Date To -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Til dato
                        </label>
                        <input type="date" id="dateTo" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="mt-4 flex justify-between items-center">
                    <button onclick="searchUsers()" 
                            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <i class="fas fa-search mr-2"></i>
                        Søg brugere
                    </button>
                    <button onclick="clearFilters()" 
                            class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-times mr-1"></i>
                        Ryd filtre
                    </button>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Søgeresultater</h3>
                    <div id="resultsCount" class="text-sm text-gray-500"></div>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="hidden px-6 py-8 text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-500">Søger efter brugere...</p>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="px-6 py-8 text-center">
                <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
                <p class="text-gray-500">Ingen brugere fundet. Prøv at justere dine søgefiltre.</p>
            </div>

            <!-- Results Table -->
            <div id="resultsTable" class="hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Bruger
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Type
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Aktivitet
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Sidste aktivitet
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Handlinger
                                </th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Dynamic content will be inserted here -->
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div id="pagination" class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Viser <span id="showingFrom">1</span> til <span id="showingTo">10</span> af <span id="totalResults">0</span> resultater
                    </div>
                    <div class="flex space-x-2">
                        <button id="prevPage" onclick="previousPage()" 
                                class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-chevron-left mr-1"></i>
                            Forrige
                        </button>
                        <button id="nextPage" onclick="nextPage()" 
                                class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                            Næste
                            <i class="fas fa-chevron-right ml-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Profile Modal -->
    <div id="userProfileModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Brugerprofil</h3>
                <button onclick="closeUserProfile()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="userProfileContent">
                <!-- Dynamic content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="admin_user_search.js"></script>
</body>
</html>
