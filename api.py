from fastapi import FastAP<PERSON>, Depends, HTTPException, File, UploadFile, Form, Query, Request, status, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, HTMLResponse, Response

from fastapi.security import OAuth2P<PERSON><PERSON><PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
import os
import shutil
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from pydantic import BaseModel # Import BaseModel
import logging
from google import genai
from google.genai import types
import traceback # Add this import at the top
import subprocess
import json
import asyncio
import time
from dotenv import load_dotenv, set_key, find_dotenv

# --- Import config FIRST ---
import config
# --- Database-first configuration - no more APP_CONFIG needed

from database import get_db, Store, Catalog, Product, CatalogPage, init_db, User, engine, get_setting, set_setting, SessionLocal, ProcessedCatalogHashes, CatalogProcessingQueue, OperationLog # Import User AND engine AND SessionLocal
from query_processor import process_natural_language_query # Remove QUERY_SYSTEM_PROMPT import here
from pdf_extractor import process_pdf_catalog_from_cloud
from cloud_storage import cloud_storage
from storage_factory import StoragePaths
from user_activity_logger import log_query_activity, log_user_interaction
from session_manager import track_page_view, increment_session_query_count
from session_tracking_middleware import SessionTrackingMiddleware, get_session_id_from_request
from advanced_interaction_logger import (
    advanced_interaction_logger, log_product_click, log_catalog_download,
    log_query_refinement, log_store_filter_change
)
from admin_data_service import admin_data_service
# --- NEW: Import the global rate_limiter instance
from gemini_parser import process_catalog_pages
import schemas # Import schemas
import auth # Import auth
from auth_utils import hash_password # Potentially needed for user creation
from operation_logger import log_operation, complete_operation, auto_reconcile_status, get_operation_history, cleanup_stale_in_memory_status

# Load environment variables
load_dotenv()

# Configure logging BEFORE defining paths with optimized format
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', datefmt='%d-%m %H:%M:%S')
logger = logging.getLogger(__name__)

# --- OAuth2 Setup ---
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/token")

# --- NEW: Global dictionary to track processing catalogs ---
PROCESSING_CATALOGS: Dict[int, Dict[str, Any]] = {}

# Function to reconfigure Gemini API across all modules
def reconfigure_gemini(api_key=None):
    """Checks for Gemini API key and logs its presence. Configuration is now handled by google-genai via environment variables."""
    if api_key is None:
        api_key = os.getenv("GOOGLE_API_KEY") # google-genai also checks for GEMINI_API_KEY
    
    if not api_key:
        logging.getLogger(__name__).warning("No Gemini API key provided or found in environment (checked GOOGLE_API_KEY/GEMINI_API_KEY). API calls may fail.")
        return False
    else:
        logging.getLogger(__name__).info("Gemini API key is set in environment. google-genai will use it automatically.")
        # The actual genai.configure() call is removed as it's no longer needed and was causing the error.
        # If other modules needed re-initialization based on API key change, that logic would go here,
        # but google-genai clients typically pick up env vars on instantiation.
        return True

app = FastAPI(title="Danish Supermarket Offers API")

# Add session tracking middleware
app.add_middleware(
    SessionTrackingMiddleware,
    session_timeout_minutes=30,  # Session timeout
    cleanup_interval_hours=24,   # Cleanup frequency
    exclude_paths=[              # Paths to exclude from tracking
        "/favicon.ico",
        "/robots.txt",
        "/health",
        "/static/",
        "/docs",
        "/redoc",
        "/openapi.json",
        "/api/admin/session-stats"  # Exclude session stats endpoint
    ]
)

# 🏗️ ENTERPRISE STARTUP EVENTS
@app.on_event("startup")
async def startup_event():
    """
    🔧 AUTOMATIC SYSTEM INITIALIZATION
    Runs automatic status reconciliation on startup to ensure system health.
    """
    logger.info("🚀 Starting Tilbudsjægeren API with enterprise monitoring...")

    # Clean up stale in-memory status entries
    try:
        cleaned_count = cleanup_stale_in_memory_status(PROCESSING_CATALOGS, max_age_hours=24)
        logger.info(f"🧹 Startup cleanup: {cleaned_count} stale in-memory entries removed")
    except Exception as e:
        logger.error(f"⚠️  Startup cleanup failed: {e}")

    # Run automatic status reconciliation on startup
    try:
        result = auto_reconcile_status()
        logger.info(f"✅ Startup reconciliation complete: {result['reconciled_count']} catalogs updated")
    except Exception as e:
        logger.error(f"⚠️  Startup reconciliation failed: {e}")

    # Log system startup
    log_operation(
        "SYSTEM", "STARTED",
        message="Tilbudsjægeren API started with enterprise monitoring",
        triggered_by="SYSTEM"
    )

# --- CORS Middleware ---
# This allows the frontend to make API calls to the backend
# Both for local development and production deployment
origins = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://************:3000",  # Local network IP for frontend
    "https://tilbudsjaegeren-website.onrender.com",  # Correct production frontend URL
    "https://tilbudsjaegeren.onrender.com",  # Alternative frontend URL (if custom domain)
    "https://avis-frontend.onrender.com",  # Legacy frontend URL (keep for compatibility)
    "http://localhost:6969",  # Local backend for testing
    "http://127.0.0.1:6969",
    "http://************:6969",  # Local network IP for backend
    "http://localhost:8001",  # Local backend on port 8001
    "http://127.0.0.1:8001",
    "http://localhost:8080",  # Admin dashboard HTTP server
    "http://127.0.0.1:8080",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for serving placeholder.png and other assets
app.mount("/static", StaticFiles(directory="static"), name="static")

# --- Load settings and initialize DB on application startup ---
@app.on_event("startup")
def startup_event():
    logger.info("Application startup sequence initiated...")
    try:
        # 1. Initialize database schema (idempotent)
        logger.info("Initializing database...")
        init_db()
        logger.info("Database tables checked/created.")

        # 2. Database-first configuration - settings loaded on-demand
        logger.info("Loading application configuration...")
        logger.warning("init_app_config() is deprecated - configuration is now fetched directly from database")
        logger.info("Application settings loaded into APP_CONFIG.")

        # 3. Check for Gemini API key (no configuration needed with new SDK)
        api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
        if not api_key:
            logger.warning("GOOGLE_API_KEY or GEMINI_API_KEY not found in environment. Gemini API calls may fail.")
        else:
            logger.info("Gemini API key found in environment. Ready for google-genai SDK.")

        # 4. Initialize cloud storage
        logger.info("Initializing cloud storage...")
        config.init_storage()
        logger.info("Cloud storage initialization complete.")

        logger.info("✅ Application startup completed successfully!")

    except Exception as e:
        logger.critical(f"❌ CRITICAL ERROR during application startup: {e}", exc_info=True)
        logger.critical("Application may not function correctly. Check database connection and environment variables.")
        # Continue running but log the critical error

# -- Initialize Database --
# Removed init_db() call from here, moved to startup event
# try:
#     print("Attempting to initialize database...")
#     init_db()
#     print("Database initialization check complete.")
# except Exception as e:
#     print(f"Database initialization failed: {e}")
# -- End Initialize Database --

# Cloud storage initialization moved to startup event

# Helper functions for cloud storage URLs
def get_cloud_storage_url(cloud_path: str) -> str:
    """Generate public URL for cloud storage file"""
    return f"https://storage.googleapis.com/{cloud_storage.bucket_name}/{cloud_path}"

def get_cloud_storage_url_from_path(cloud_path: str) -> str:
    """
    Convert any cloud storage path to full public URL
    Uses StoragePaths for consistent path handling
    """
    if not cloud_path:
        return None

    # Check if it's already a full URL
    if cloud_path.startswith('http'):
        return cloud_path

    # Generate full cloud storage URL
    return get_cloud_storage_url(cloud_path)

def get_logo_url(store_name: str) -> str:
    """Generate logo URL for a store based on actual files in gs://tilbudsjaegeren/logos/supermarkets/"""
    # Direct mapping based on actual files in Google Cloud Storage
    # This covers all stores from db_init.py, frontend mappings, and actual GCS files
    store_logo_map = {
        # Main store names (as they appear in database)
        "365discount": "365discount.png",
        "aldi": "365discount.png",  # Fallback since aldi.png doesn't exist in GCS
        "bilka": "bilka.png",
        "brugsen": "brugsen.png",
        "superbrugsen": "superbrugsen.png",
        "føtex": "fotex.png",  # Note: ø -> o in filename
        "fotex": "fotex.png",
        "let-køb": "letkoeb.jpg",  # Note: special chars and .jpg extension
        "letkoeb": "letkoeb.jpg",
        "lidl": "lidl.png",
        "meny": "meny.png",
        "min købmand": "minkobmand.png",  # Note: space removed, ø -> o
        "minkobmand": "minkobmand.png",
        "minkoebmand": "minkoebmand.png",  # Handle both variants (duplicate files in GCS)
        "netto": "netto.svg",  # Note: .svg extension, not .png
        "rema 1000": "rema1000.png",  # Note: space removed
        "rema1000": "rema1000.png",
        "spar": "spar.png",

        # Additional variations and case handling
        "Netto": "netto.svg",
        "Føtex": "fotex.png",
        "Bilka": "bilka.png",
        "Rema 1000": "rema1000.png",
        "Lidl": "lidl.png",
        "Aldi": "365discount.png",  # Fallback
        "Spar": "spar.png",
        "Min Købmand": "minkobmand.png",
        "Brugsen": "brugsen.png",
        "SuperBrugsen": "superbrugsen.png",
        "Let-Køb": "letkoeb.jpg",
        "Meny": "meny.png"
    }

    # Try exact match first (preserves case)
    if store_name in store_logo_map:
        filename = store_logo_map[store_name]
        cloud_path = f"logos/supermarkets/{filename}"
        return get_cloud_storage_url(cloud_path)

    # Try normalized lookup
    normalized_name = store_name.lower().strip()
    if normalized_name in store_logo_map:
        filename = store_logo_map[normalized_name]
        cloud_path = f"logos/supermarkets/{filename}"
        return get_cloud_storage_url(cloud_path)

    # Fallback to default logo if store not found
    logger.warning(f"No logo found for store: '{store_name}', using default owl logo")
    return get_cloud_storage_url("logos/owl.png")



# --- NEW: Background task wrapper for PDF re-processing ---
async def reprocess_catalog_pdf_background(
    catalog_id: int,
    model_name: Optional[str],
    status_dict: Dict[int, Dict[str, Any]]
):
    """🚨 ENTERPRISE Background task to re-process PDF from cloud storage to create missing pages."""
    db_session = None
    operation_log_id = None

    try:
        # Each background task needs its own DB session
        db_session = SessionLocal()
        logger.info(f"Background PDF re-processing started for catalog ID {catalog_id}.")

        # 📝 Log operation start
        operation_log_id = log_operation(
            "PARSE", "STARTED",
            catalog_id=catalog_id,
            message=f"Re-processing PDF for catalog {catalog_id} to create missing pages",
            metadata={"model_name": model_name, "operation_type": "pdf_reprocess"},
            triggered_by="USER"
        )

        status_dict[catalog_id] = {
            "status": "processing",
            "message": "Re-processing PDF from cloud storage...",
            "last_updated": datetime.now().isoformat()
        }

        # Get catalog and store info
        catalog = db_session.query(Catalog).filter(Catalog.id == catalog_id).first()
        if not catalog:
            raise Exception(f"Catalog {catalog_id} not found")

        store = db_session.query(Store).filter(Store.id == catalog.store_id).first()
        if not store:
            raise Exception(f"Store not found for catalog {catalog_id}")

        # Re-process the PDF from cloud storage to create pages
        from pdf_extractor import process_pdf_catalog_from_cloud

        import asyncio
        import concurrent.futures

        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            processed_catalog, image_paths = await loop.run_in_executor(
                executor,
                process_pdf_catalog_from_cloud,
                catalog.pdf_path,
                store.name,
                catalog.title,
                catalog.valid_from,
                catalog.valid_to,
                False,  # attempt_date_extraction
                model_name,
                None,  # dpi - use intelligent selection
                db_session
            )

        if processed_catalog and image_paths:
            page_count = len(image_paths)
            logger.info(f"✅ Successfully re-processed PDF for catalog {catalog_id}: {page_count} pages created")

            # Now process the pages with Gemini
            status_dict[catalog_id] = {
                "status": "processing",
                "message": f"Processing {page_count} pages with Gemini...",
                "last_updated": datetime.now().isoformat()
            }

            from gemini_parser import process_catalog_pages

            with concurrent.futures.ThreadPoolExecutor() as executor:
                product_count = await loop.run_in_executor(
                    executor,
                    process_catalog_pages,
                    catalog_id,
                    model_name
                )

            status_dict[catalog_id] = {
                "status": "completed",
                "message": f"Completed. Created {page_count} pages, found {product_count} products.",
                "last_updated": datetime.now().isoformat()
            }
            logger.info(f"Background PDF re-processing complete for catalog {catalog_id}. Pages: {page_count}, Products: {product_count}")

            # Complete the operation log
            complete_operation(operation_log_id, "SUCCESS", f"Successfully re-processed PDF and found {product_count} products.")

            # Update database status tracking
            _update_database_status(catalog_id, 'SUCCESS', f"Successfully re-processed PDF and found {product_count} products.")

        else:
            raise Exception("PDF re-processing failed - no pages created")

    except Exception as e:
        error_msg = f"Error re-processing PDF for catalog {catalog_id}: {str(e)}"
        logger.error(error_msg, exc_info=True)
        status_dict[catalog_id] = {
            "status": "error",
            "message": error_msg,
            "last_updated": datetime.now().isoformat()
        }

        # Complete the operation log with failure
        if operation_log_id:
            complete_operation(operation_log_id, "FAILED", error_msg)

        # Update database status tracking
        _update_database_status(catalog_id, 'FAILED', error_msg)

    finally:
        if db_session:
            db_session.close()

# --- NEW: Background task wrapper for parsing ---
async def process_catalog_pages_background(
    catalog_id: int,
    model_name: Optional[str],
    status_dict: Dict[int, Dict[str, Any]]
):
    """🏗️ ENTERPRISE Background task with comprehensive operation logging."""
    db_session = None
    operation_log_id = None

    try:
        # Each background task needs its own DB session
        db_session = SessionLocal()
        logger.info(f"Background task started for catalog ID {catalog_id}.")

        # 📝 Log operation start
        operation_log_id = log_operation(
            "PARSE", "STARTED",
            catalog_id=catalog_id,
            message=f"Starting background parsing with model {model_name}",
            metadata={"model_name": model_name},
            triggered_by="USER"
        )

        status_dict[catalog_id] = {
            "status": "processing",
            "message": "Parsing pages...",
            "last_updated": datetime.now().isoformat()
        }

        # Call the actual processing function in a thread pool to avoid blocking
        import asyncio
        import concurrent.futures

        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            product_count = await loop.run_in_executor(
                executor,
                process_catalog_pages,
                catalog_id,
                model_name
            )
        
        # Check final status after processing
        if catalog_id in status_dict and status_dict[catalog_id].get("status") == "cancelling":
            status_dict[catalog_id]["status"] = "cancelled"
            status_dict[catalog_id]["message"] = "Processing cancelled by user."
            status_dict[catalog_id]["last_updated"] = datetime.now().isoformat()
            logger.info(f"Background parsing for catalog {catalog_id} was cancelled. Products found before cancel: {product_count}")

            # Update database status for cancelled processing
            _update_database_status(catalog_id, "CANCELLED", f"Processing cancelled by user. Products found before cancel: {product_count}")

            # 📝 Log cancellation
            if operation_log_id:
                complete_operation(
                    operation_log_id, "CANCELLED",
                    message=f"Processing cancelled by user. Products found: {product_count}",
                    metadata={"products_found": product_count}
                )

        elif catalog_id in status_dict: # If not cancelled, it completed
            status_dict[catalog_id]["status"] = "completed"
            status_dict[catalog_id]["message"] = f"Successfully parsed and found {product_count} products."
            status_dict[catalog_id]["products_found"] = product_count
            status_dict[catalog_id]["last_updated"] = datetime.now().isoformat()
            logger.info(f"Background parsing complete for catalog {catalog_id}. Products found: {product_count}")

            # 🔧 CRITICAL FIX: Update database status for successful completion
            _update_database_status(catalog_id, "SUCCESS", f"Successfully parsed and found {product_count} products.")

            # 📝 Log success
            if operation_log_id:
                complete_operation(
                    operation_log_id, "SUCCESS",
                    message=f"Successfully parsed and found {product_count} products",
                    metadata={"products_found": product_count}
                )

            # 🔧 AUTOMATIC STATUS RECONCILIATION - Run after successful parsing
            auto_reconcile_status()

            # 🧹 AUTOMATIC CLEANUP - Remove stale in-memory entries
            cleanup_stale_in_memory_status(status_dict, max_age_hours=6)

        else: # Should not happen if entry was created
            logger.warning(f"Catalog ID {catalog_id} not found in status_dict after processing.")
            
    except Exception as e:
        logger.error(f"Error in background task for catalog ID {catalog_id}: {str(e)}", exc_info=True)
        if catalog_id in status_dict:
            status_dict[catalog_id]["status"] = "error"
            status_dict[catalog_id]["message"] = f"Error during parsing: {str(e)}"
            status_dict[catalog_id]["last_updated"] = datetime.now().isoformat()

            # 🔧 CRITICAL FIX: Update database status for errors
            _update_database_status(catalog_id, "FAILED", f"Error during parsing: {str(e)}")

            # 📝 Log error
            if operation_log_id:
                complete_operation(
                    operation_log_id, "FAILED",
                    message=f"Error during parsing: {str(e)}",
                    metadata={"error": str(e)}
                )
    finally:
        if db_session:
            db_session.close()
        # Optionally remove completed/errored tasks from dict after some time or if not needed for status anymore
        # For now, keep them for status checking.

def _update_database_status(catalog_id: int, status: str, message: str):
    """
    🔧 ENTERPRISE STATUS TRACKING FIX
    Updates both CatalogProcessingQueue and ProcessedCatalogHashes status when processing completes.
    This fixes the critical status mismatch between in-memory and database status.

    Args:
        catalog_id: The catalog ID to update
        status: New status (SUCCESS, FAILED, CANCELLED)
        message: Status message for logging
    """
    db = SessionLocal()
    try:
        # Update CatalogProcessingQueue status
        queue_entry = db.query(CatalogProcessingQueue).filter(
            CatalogProcessingQueue.catalog_db_id == catalog_id
        ).first()

        if queue_entry:
            queue_entry.status = status
            queue_entry.last_attempt_at = datetime.now()
            logger.info(f"✅ Updated CatalogProcessingQueue for catalog {catalog_id}: {status}")
        else:
            logger.warning(f"⚠️  No CatalogProcessingQueue entry found for catalog {catalog_id}")

        # Update ProcessedCatalogHashes status
        hash_entry = db.query(ProcessedCatalogHashes).filter(
            ProcessedCatalogHashes.catalog_db_id == catalog_id
        ).first()

        if hash_entry:
            hash_entry.status = status
            hash_entry.processed_at = datetime.now()
            logger.info(f"✅ Updated ProcessedCatalogHashes for catalog {catalog_id}: {status}")
        else:
            logger.warning(f"⚠️  No ProcessedCatalogHashes entry found for catalog {catalog_id}")

        # Commit the changes
        db.commit()
        logger.info(f"🎯 Database status successfully updated for catalog {catalog_id}: {status} - {message}")

    except Exception as e:
        logger.error(f"❌ Error updating database status for catalog {catalog_id}: {e}", exc_info=True)
        db.rollback()
    finally:
        db.close()

# API routes
@app.get("/health/db")
async def database_health_check():
    """Database health check endpoint with connection retry"""
    try:
        with SessionLocal() as db:
            # Simple query to test database connectivity
            result = db.execute("SELECT 1").fetchone()
            return {
                "status": "healthy",
                "database": "connected",
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )

@app.get("/stores", response_model=List[dict])
async def get_stores(
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    # Get session ID from middleware (automatic session tracking)
    session_id = get_session_id_from_request(request)

    # Log page view interaction
    log_user_interaction(
        interaction_type="page_viewed",
        user_id=current_user.id if current_user else None,
        session_id=session_id,
        page_url=str(request.url),
        interaction_data={"page_type": "store_list"}
    )

    stores = db.query(Store).all()
    return [{"id": store.id, "name": store.name, "logo_url": get_logo_url(str(store.name))} for store in stores]


@app.post("/log-store-selection")
async def log_store_selection(
    request: Request,
    store_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log when user selects/deselects stores for search"""
    # Get session ID from middleware (automatic session tracking)
    session_id = get_session_id_from_request(request)

    selected_stores = store_data.get("selected_stores", [])

    # Log store selection interaction
    log_user_interaction(
        interaction_type="store_selected",
        user_id=current_user.id if current_user else None,
        session_id=session_id,
        interaction_data={
            "selected_stores": selected_stores,
            "store_count": len(selected_stores)
        }
    )

    return {"status": "logged", "selected_stores": selected_stores}


@app.post("/log-product-click")
async def log_product_click_endpoint(
    request: Request,
    interaction_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log when user clicks on a product in search results"""
    session_id = get_session_id_from_request(request)

    interaction_id = log_product_click(
        product_id=interaction_data.get("product_id"),
        product_name=interaction_data.get("product_name", ""),
        product_price=interaction_data.get("product_price"),
        store_id=interaction_data.get("store_id"),
        store_name=interaction_data.get("store_name"),
        source_query_id=interaction_data.get("source_query_id"),
        position_in_results=interaction_data.get("position_in_results"),
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )

    return {"status": "logged", "interaction_id": interaction_id}


@app.post("/log-catalog-download")
async def log_catalog_download_endpoint(
    request: Request,
    interaction_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log when user downloads a catalog PDF"""
    session_id = get_session_id_from_request(request)

    interaction_id = log_catalog_download(
        catalog_id=interaction_data.get("catalog_id"),
        catalog_title=interaction_data.get("catalog_title", ""),
        store_id=interaction_data.get("store_id"),
        store_name=interaction_data.get("store_name", ""),
        file_size_bytes=interaction_data.get("file_size_bytes"),
        source_query_id=interaction_data.get("source_query_id"),
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )

    return {"status": "logged", "interaction_id": interaction_id}


@app.post("/log-query-refinement")
async def log_query_refinement_endpoint(
    request: Request,
    interaction_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log when user refines their search query"""
    session_id = get_session_id_from_request(request)

    interaction_id = log_query_refinement(
        original_query=interaction_data.get("original_query", ""),
        refined_query=interaction_data.get("refined_query", ""),
        original_query_id=interaction_data.get("original_query_id"),
        refinement_type=interaction_data.get("refinement_type", "manual"),
        time_between_queries_seconds=interaction_data.get("time_between_queries_seconds"),
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )

    return {"status": "logged", "interaction_id": interaction_id}


@app.post("/log-store-filter-change")
async def log_store_filter_change_endpoint(
    request: Request,
    interaction_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log when user changes store selection filters"""
    session_id = get_session_id_from_request(request)

    interaction_id = log_store_filter_change(
        previous_stores=interaction_data.get("previous_stores", []),
        new_stores=interaction_data.get("new_stores", []),
        change_type=interaction_data.get("change_type", "manual"),
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )

    return {"status": "logged", "interaction_id": interaction_id}


@app.post("/log-ui-interaction")
async def log_ui_interaction_endpoint(
    request: Request,
    interaction_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log interactions with specific UI elements"""
    session_id = get_session_id_from_request(request)

    interaction_id = advanced_interaction_logger.log_ui_element_interaction(
        element_type=interaction_data.get("element_type", ""),
        element_id=interaction_data.get("element_id", ""),
        action=interaction_data.get("action", "click"),
        element_text=interaction_data.get("element_text"),
        page_url=interaction_data.get("page_url"),
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )

    return {"status": "logged", "interaction_id": interaction_id}


@app.post("/log-error-encounter")
async def log_error_encounter_endpoint(
    request: Request,
    interaction_data: dict,
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Log when user encounters an error"""
    session_id = get_session_id_from_request(request)

    interaction_id = advanced_interaction_logger.log_user_error_encounter(
        error_type=interaction_data.get("error_type", "unknown"),
        error_message=interaction_data.get("error_message", ""),
        page_url=interaction_data.get("page_url"),
        user_action_before_error=interaction_data.get("user_action_before_error"),
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )

    return {"status": "logged", "interaction_id": interaction_id}


@app.get("/api/admin/session-stats")
async def get_session_stats(admin_user: schemas.User = Depends(auth.require_admin)):
    """Get current session statistics for monitoring"""
    db = next(get_db())
    try:
        from datetime import datetime, timedelta

        # Get session statistics from database
        total_sessions = db.query(UserSession).count()

        # Active sessions (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        active_sessions = db.query(UserSession).filter(
            UserSession.session_start >= yesterday
        ).count()

        # Total queries and interactions
        total_queries = db.query(UserQuery).count()
        total_interactions = db.query(UserInteraction).count()

        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "total_queries": total_queries,
            "total_interactions": total_interactions,
            "last_updated": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting session stats: {str(e)}")
        return {
            "error": f"Database error: {str(e)}",
            "total_sessions": 0,
            "active_sessions": 0,
            "total_queries": 0,
            "total_interactions": 0
        }
    finally:
        db.close()


# --- Admin User Management Endpoints ---

@app.get("/api/admin/users/search")
async def admin_search_users(
    search_term: Optional[str] = Query(None, description="Search term for username"),
    user_type: Optional[str] = Query(None, description="User type filter"),
    date_from: Optional[str] = Query(None, description="Date from (ISO format)"),
    date_to: Optional[str] = Query(None, description="Date to (ISO format)"),
    limit: int = Query(50, description="Number of results per page"),
    offset: int = Query(0, description="Offset for pagination"),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """Search users with filters and pagination"""
    try:
        # Parse date filters
        date_from_dt = None
        date_to_dt = None
        if date_from:
            date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
        if date_to:
            date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))

        # Search users
        users, total_count = admin_data_service.search_users(
            search_term=search_term,
            user_type=user_type,
            date_from=date_from_dt,
            date_to=date_to_dt,
            limit=limit,
            offset=offset
        )

        # Log admin action
        from user_activity_logger import activity_logger
        activity_logger.log_admin_action(
            admin_user_id=admin_user.id,
            action_type="user_searched",
            action_details={
                "search_term": search_term,
                "user_type": user_type,
                "results_count": len(users)
            }
        )

        return {
            "users": users,
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total_count
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in admin_search_users: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/admin/users/{user_id}/profile")
async def admin_get_user_profile(
    user_id: int,
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """Get comprehensive user profile with activity summary"""
    try:
        profile = admin_data_service.get_user_profile(user_id)

        if not profile:
            raise HTTPException(status_code=404, detail="User not found")

        # Log admin action
        from user_activity_logger import activity_logger
        activity_logger.log_admin_action(
            admin_user_id=admin_user.id,
            action_type="user_viewed",
            target_user_id=user_id,
            resource_accessed="user_profile"
        )

        return profile

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in admin_get_user_profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/admin/users/{user_id}/timeline")
async def admin_get_user_timeline(
    user_id: int,
    activity_type: Optional[str] = Query(None, description="Activity type filter"),
    date_from: Optional[str] = Query(None, description="Date from (ISO format)"),
    date_to: Optional[str] = Query(None, description="Date to (ISO format)"),
    limit: int = Query(100, description="Number of activities to return"),
    offset: int = Query(0, description="Offset for pagination"),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """Get user's activity timeline"""
    try:
        # Parse date filters
        date_from_dt = None
        date_to_dt = None
        if date_from:
            date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
        if date_to:
            date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))

        # Get timeline
        timeline = admin_data_service.get_user_activity_timeline(
            user_id=user_id,
            activity_type=activity_type,
            date_from=date_from_dt,
            date_to=date_to_dt,
            limit=limit,
            offset=offset
        )

        # Log admin action
        from user_activity_logger import activity_logger
        activity_logger.log_admin_action(
            admin_user_id=admin_user.id,
            action_type="user_timeline_viewed",
            target_user_id=user_id,
            resource_accessed="activity_timeline",
            action_details={
                "activity_type": activity_type,
                "activities_count": len(timeline)
            }
        )

        return {
            "user_id": user_id,
            "timeline": timeline,
            "activity_type": activity_type,
            "limit": limit,
            "offset": offset
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in admin_get_user_timeline: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


# --- Admin Analytics Endpoints ---

@app.get("/api/admin/analytics/system")
async def admin_get_system_analytics(
    date_from: Optional[str] = Query(None, description="Date from (ISO format)"),
    date_to: Optional[str] = Query(None, description="Date to (ISO format)"),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """Get system-wide analytics and metrics"""
    try:
        # Parse date filters
        date_from_dt = None
        date_to_dt = None
        if date_from:
            date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
        if date_to:
            date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))

        # Get analytics
        analytics = admin_data_service.get_system_analytics(
            date_from=date_from_dt,
            date_to=date_to_dt
        )

        # Log admin action
        from user_activity_logger import activity_logger
        activity_logger.log_admin_action(
            admin_user_id=admin_user.id,
            action_type="system_analytics_viewed",
            resource_accessed="system_metrics"
        )

        return analytics

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in admin_get_system_analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/admin/analytics/queries")
async def admin_get_query_analytics(
    date_from: Optional[str] = Query(None, description="Date from (ISO format)"),
    date_to: Optional[str] = Query(None, description="Date to (ISO format)"),
    limit: int = Query(50, description="Number of results to return"),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """Get detailed query analytics and patterns"""
    try:
        # Parse date filters
        date_from_dt = None
        date_to_dt = None
        if date_from:
            date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
        if date_to:
            date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))

        # Get query analytics
        analytics = admin_data_service.get_query_analytics(
            date_from=date_from_dt,
            date_to=date_to_dt,
            limit=limit
        )

        # Log admin action (using the activity logger directly)
        from user_activity_logger import activity_logger
        activity_logger.log_admin_action(
            admin_user_id=admin_user.id,
            action_type="query_analytics_viewed",
            resource_accessed="query_metrics"
        )

        return analytics

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in admin_get_query_analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/admin/users/{user_id}/queries")
async def admin_get_user_queries(
    user_id: int,
    date_from: Optional[str] = Query(None, description="Date from (ISO format)"),
    date_to: Optional[str] = Query(None, description="Date to (ISO format)"),
    limit: int = Query(100, description="Number of queries to return"),
    offset: int = Query(0, description="Offset for pagination"),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """Get user's query history with details"""
    try:
        db = next(get_db())

        # Build query filter
        query_filter = UserQuery.user_id == user_id
        if date_from:
            date_from_dt = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            query_filter = and_(query_filter, UserQuery.created_at >= date_from_dt)
        if date_to:
            date_to_dt = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            query_filter = and_(query_filter, UserQuery.created_at <= date_to_dt)

        # Get queries
        queries = db.query(UserQuery).filter(query_filter)\
                   .order_by(desc(UserQuery.created_at))\
                   .offset(offset).limit(limit).all()

        # Get total count
        total_count = db.query(func.count(UserQuery.id)).filter(query_filter).scalar()

        # Format results
        query_list = []
        for query in queries:
            query_list.append({
                'id': query.id,
                'query_text': query.query_text,
                'selected_stores': query.selected_stores,
                'query_model': query.query_model,
                'results_count': query.results_count,
                'response_time_ms': query.response_time_ms,
                'was_successful': query.was_successful,
                'error_message': query.error_message,
                'query_category': query.query_category,
                'query_intent': query.query_intent,
                'created_at': query.created_at.isoformat()
            })

        # Log admin action
        from user_activity_logger import activity_logger
        activity_logger.log_admin_action(
            admin_user_id=admin_user.id,
            action_type="user_queries_viewed",
            target_user_id=user_id,
            resource_accessed="query_history",
            action_details={
                "queries_count": len(query_list)
            }
        )

        return {
            "user_id": user_id,
            "queries": query_list,
            "total_count": total_count,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total_count
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
    except Exception as e:
        logger.error(f"Error in admin_get_user_queries: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
    finally:
        db.close()

@app.get("/catalogs", response_model=List[dict])
async def get_catalogs():
    """
    High-frequency endpoint optimized with self-managed database session
    to prevent connection pool exhaustion from frequent polling.
    Includes automatic retry logic for database connection issues.
    """
    max_retries = 3
    retry_delay = 1  # seconds

    for attempt in range(max_retries):
        try:
            # Use self-managed database session for immediate connection release
            with SessionLocal() as db:
                catalogs = db.query(Catalog).order_by(Catalog.created_at.desc()).all()
                result = []
                for catalog in catalogs:
                    first_page = db.query(CatalogPage).filter(CatalogPage.catalog_id == catalog.id, CatalogPage.page_number == 1).first()
                    # Convert image path to cloud storage URL using helper function
                    first_page_image_url = get_cloud_storage_url_from_path(first_page.image_path) if first_page else None

                    # Safely access store name
                    store_name = catalog.store.name if catalog.store else "Unknown Store"

                    result.append({
                        "id": catalog.id,
                        "store_id": catalog.store_id,
                        "store_name": store_name, # Use the safe variable
                        "store_logo_url": get_logo_url(store_name), # Add store logo URL from cloud storage
                        "title": catalog.title,
                        "valid_from": catalog.valid_from,
                        "valid_to": catalog.valid_to,
                        "pages": len(catalog.pages),
                        "products": len(catalog.products),
                        "first_page_image_url": first_page_image_url, # Add the first page image URL
                        "pdf_path": catalog.pdf_path # Add PDF path for modal viewer
                    })
                return result

        except Exception as e:
            error_msg = str(e).lower()
            is_connection_error = any(keyword in error_msg for keyword in [
                'connection', 'server closed', 'timeout', 'network', 'unreachable'
            ])

            if is_connection_error and attempt < max_retries - 1:
                logger.warning(f"Database connection error on attempt {attempt + 1}/{max_retries}: {str(e)}")
                logger.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
                continue
            else:
                logger.error(f"Error fetching catalogs (attempt {attempt + 1}/{max_retries}): {str(e)}\n{traceback.format_exc()}")
                # Raise HTTPException to return a proper error response
                raise HTTPException(status_code=500, detail=f"Internal server error fetching catalogs.")

@app.get("/catalogs/{catalog_id}", response_model=dict)
async def get_catalog_by_id(
    catalog_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Get a specific catalog by ID with PDF URL for viewer"""
    catalog = db.query(Catalog).filter(Catalog.id == catalog_id).first()
    if not catalog:
        raise HTTPException(status_code=404, detail="Catalog not found")

    # Get session ID from middleware (automatic session tracking)
    session_id = get_session_id_from_request(request)

    # Get source query ID from query parameters (if available)
    source_query_id = request.query_params.get("query_id")

    # Start catalog view timer for duration tracking
    advanced_interaction_logger.start_catalog_view_timer(catalog_id, session_id)

    # Log enhanced catalog view interaction
    advanced_interaction_logger.log_catalog_view_with_duration(
        catalog_id=catalog_id,
        catalog_title=catalog.title,
        store_id=catalog.store_id,
        store_name=catalog.store.name if catalog.store else "Unknown",
        source_query_id=int(source_query_id) if source_query_id else None,
        user_id=current_user.id if current_user else None,
        session_id=session_id
    )

    # Get first page image
    first_page = db.query(CatalogPage).filter(CatalogPage.catalog_id == catalog.id, CatalogPage.page_number == 1).first()
    first_page_image_url = get_cloud_storage_url_from_path(first_page.image_path) if first_page else None

    # Get store name safely
    store_name = catalog.store.name if catalog.store else "Unknown Store"

    # Get PDF URL from Google Cloud Storage
    pdf_url = get_cloud_storage_url_from_path(catalog.pdf_path) if catalog.pdf_path else None

    return {
        "id": catalog.id,
        "store_id": catalog.store_id,
        "store_name": store_name,
        "store_logo_url": get_logo_url(store_name),
        "title": catalog.title,
        "pdf_url": pdf_url,
        "first_page_image_url": first_page_image_url,
        "created_at": catalog.created_at.isoformat(),
        "valid_from": catalog.valid_from.isoformat() if catalog.valid_from else None,
        "valid_to": catalog.valid_to.isoformat() if catalog.valid_to else None,
        "pages": [
            {
                "id": page.id,
                "page_number": page.page_number,
                "image_url": get_cloud_storage_url_from_path(page.image_path)
            } for page in catalog.pages
        ],
        "products": [
            {
                "id": product.id,
                "name": product.name,
                "description": product.description,
                "price": product.price,
                "original_price": product.original_price,
                "unit": product.unit,
                "category": product.category,
                "page_number": product.page_number
            } for product in catalog.products
        ]
    }


@app.post("/upload_catalog")
async def upload_catalog(
    background_tasks: BackgroundTasks, # Moved up
    db: Session = Depends(get_db),
    admin_user: schemas.User = Depends(auth.require_admin),
    file: UploadFile = File(...),
    store_id: str = Form(...),
    new_store_name: Optional[str] = Form(None),
    title: Optional[str] = Form(None),
    valid_from: Optional[str] = Form(None),
    valid_to: Optional[str] = Form(None),
    skip_parsing: bool = Form(False),
    parsing_model: Optional[str] = Form(None)
):
    # Handle store selection or creation
    store = None
    if store_id == "__add_new__":
        if not new_store_name:
            raise HTTPException(status_code=400, detail="New store name is required when adding a new store.")
        # Check if store already exists (case-insensitive)
        existing_store = db.query(Store).filter(Store.name.ilike(new_store_name)).first()
        if existing_store:
            store = existing_store
            logger.info(f"Using existing store '{store.name}' found by name.")
        else:
            # Create new store (logo_url can be added later)
            store = Store(name=new_store_name)
            db.add(store)
            db.commit()
            db.refresh(store)
            logger.info(f"Created new store: {store.name} (ID: {store.id})")
    else:
        try:
            store_id_int = int(store_id)
            store = db.query(Store).filter(Store.id == store_id_int).first()
            if not store:
                raise HTTPException(status_code=404, detail=f"Store with ID {store_id_int} not found")
        except ValueError:
             raise HTTPException(status_code=400, detail=f"Invalid store ID format: {store_id}")
    
    # --- Save Uploaded File Temporarily ---
    # FastAPI saves uploads to a temporary location. We need that path.
    # Or we can read the file content and save it to our own temp file.
    # Let's create our own secure temporary file.
    temp_file_path = None
    try:
        # Create a temporary file to securely handle the upload
        # Use a context manager to ensure cleanup
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name # Get the path to the saved temp file
        logger.info(f"Saved uploaded file temporarily to: {temp_file_path}")

        # Parse dates
        valid_from_date = datetime.strptime(valid_from, "%Y-%m-%d") if valid_from else datetime.now()
        valid_to_date = datetime.strptime(valid_to, "%Y-%m-%d") if valid_to else None
        
        # Determine if date extraction should be attempted
        # Attempt only if user did *not* provide both dates
        attempt_extraction = not (valid_from and valid_to)

        # Process the catalog using cloud storage
        # First upload the temp file to cloud storage, then process from cloud
        from cloud_storage import upload_catalog_pdf

        # Upload PDF to cloud storage
        cloud_pdf_path = upload_catalog_pdf(
            temp_file_path,
            str(store.name),
            valid_from_date.strftime("%Y%m%d") if valid_from_date else "unknown",
            valid_to_date.strftime("%Y%m%d") if valid_to_date else "unknown"
        )

        if not cloud_pdf_path:
            raise HTTPException(status_code=500, detail="Failed to upload PDF to cloud storage")

        # 🚨 CRITICAL: Process the catalog from cloud storage
        # This MUST succeed or we'll have orphaned catalogs
        try:
            catalog, image_paths_relative = process_pdf_catalog_from_cloud(
                cloud_pdf_path=cloud_pdf_path,
                store_name=str(store.name),
                title=title,
                valid_from=valid_from_date,
                valid_to=valid_to_date,
                attempt_date_extraction=attempt_extraction,
                parsing_model=parsing_model,
                db=db
            )
        except Exception as pdf_error:
            logger.error(f"❌ CRITICAL PDF PROCESSING FAILURE: {pdf_error}")
            raise HTTPException(
                status_code=500,
                detail=f"PDF processing failed: {str(pdf_error)[:200]}. Check cloud storage connectivity."
            )

        if not catalog:
            # This should not happen with new error handling, but keep as safety net
            raise HTTPException(status_code=500, detail="Failed to process catalog after upload")
        
        result = {
            "catalog_id": catalog.id,
            "title": catalog.title,
            "pages": len(image_paths_relative),
            "products": 0
        }
        
        # Trigger parsing if not skipped
        if not skip_parsing:
            logger.info(f"Adding background task for parsing catalog ID {catalog.id}")
            # Set initial status
            catalog_id_int = catalog.id
            PROCESSING_CATALOGS[catalog_id_int] = {"status": "queued", "message": "Parsing task queued."}
            background_tasks.add_task(
                process_catalog_pages_background,
                catalog_id=catalog_id_int,
                model_name=parsing_model,
                status_dict=PROCESSING_CATALOGS
            )
            # Update result to indicate background processing
            result["message"] = "Catalog uploaded. Parsing started in background."
            # result["products"] will not be known immediately
        else:
            result["message"] = "Catalog uploaded. Parsing skipped."
            PROCESSING_CATALOGS[catalog.id] = {"status": "skipped", "message": "Parsing skipped by user."}

        return JSONResponse(status_code=200, content=result)
    
    except HTTPException as http_exc:
         # Re-raise HTTPExceptions
         raise http_exc
    except Exception as e:
        logger.error(f"Error during catalog upload or processing: {str(e)}", exc_info=True)
        # Return a generic error
        raise HTTPException(status_code=500, detail=f"Internal server error during upload: {str(e)}")
    finally:
        # --- Clean up the temporary file --- 
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"Cleaned up temporary file: {temp_file_path}")
            except OSError as e:
                logger.error(f"Error cleaning up temporary file {temp_file_path}: {e}")
        # Ensure the uploaded file handle is closed by FastAPI
        await file.close() 

@app.get("/products", response_model=List[dict])
async def get_products(
    category: Optional[str] = Query(None),
    store_id: Optional[int] = Query(None),
    db: Session = Depends(get_db)
):
    query = db.query(Product)
    
    if category:
        query = query.filter(Product.category.ilike(f"%{category}%"))
    
    if store_id:
        query = query.join(Catalog).filter(Catalog.store_id == store_id)
    
    products = query.all()
    
    return [{
        "id": product.id,
        "name": product.name,
        "description": product.description,
        "price": product.price,
        "original_price": product.original_price,
        "unit": product.unit,
        "category": product.category,
        "catalog_id": product.catalog_id,
        "image_path": product.image_path,
        # Enhanced fields
        "quantity": product.quantity,
        "unit_type": product.unit_type,
        "price_per_base_unit": product.price_per_base_unit,
        "base_unit_type": product.base_unit_type,
        "brand": product.brand
    } for product in products]

# Pydantic model for the /ask request body
class AskRequest(BaseModel):
    query: str
    query_model: Optional[str] = None
    selected_catalog_ids: Optional[List[int]] = None


@app.get("/query")
async def query_offers_get_legacy(
    request: Request,
    q: str = Query(..., description="The natural language query"),
    catalog_ids: Optional[str] = Query(None, description="Comma-separated catalog IDs"),
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Legacy GET endpoint for backward compatibility - redirects to POST logic"""
    try:
        # Get session ID from middleware (automatic session tracking)
        session_id = get_session_id_from_request(request)

        # Parse catalog IDs if provided
        selected_catalog_ids = None
        if catalog_ids:
            try:
                selected_catalog_ids = [int(id.strip()) for id in catalog_ids.split(',') if id.strip()]
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid catalog_ids format")

        # Create AskRequest object to reuse the same logic
        ask_request = AskRequest(
            query=q,
            selected_catalog_ids=selected_catalog_ids
        )

        # Reuse the POST endpoint logic
        return await ask_query_internal(ask_request, db, current_user, session_id)

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        logger.error(f"Error processing legacy GET query: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@app.post("/ask") # Change to POST and define endpoint
async def ask_query(
    request_obj: AskRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[schemas.User] = Depends(auth.try_get_current_user)
):
    """Process a natural language query via POST (preferred method)"""
    # Get session ID from middleware (automatic session tracking)
    session_id = get_session_id_from_request(request)

    return await ask_query_internal(request_obj, db, current_user, session_id)

async def ask_query_internal(request: AskRequest, db: Session, current_user: Optional[schemas.User], session_id: Optional[str] = None):
    """Internal function to process natural language queries - shared by GET and POST endpoints"""
    start_time = time.time()
    query_id = None

    try:
        # Process the query
        query_result = process_natural_language_query(
            request.query,
            model_name=request.query_model,
            selected_catalog_ids=request.selected_catalog_ids
        )

        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)

        # Check if the query processor returned an error
        if query_result.get("error"):
            logger.warning(f"Query processor returned an error message: {query_result.get('answer')}")

            # Log failed query
            query_id = log_query_activity(
                query_text=request.query,
                results_count=0,
                response_time_ms=response_time_ms,
                was_successful=False,
                error_message=query_result.get('answer', 'Unknown error in query processing'),
                user_id=current_user.id if current_user else None,
                session_id=session_id,
                selected_stores=request.selected_catalog_ids,
                query_model=request.query_model or 'gemini-2.5-flash-lite'
            )

            return {
                "response": query_result.get('answer', 'An unknown error occurred in query processing.'),
                "products": [], # Return empty list on error
                "query_id": query_id
            }

        # Extract products and log successful query
        products = query_result.get('products', [])

        # Log successful query
        query_id = log_query_activity(
            query_text=request.query,
            results_count=len(products),
            response_time_ms=response_time_ms,
            was_successful=True,
            user_id=current_user.id if current_user else None,
            session_id=session_id,
            selected_stores=request.selected_catalog_ids,
            query_model=request.query_model or 'gemini-2.5-flash-lite'
        )

        # Increment session query count
        if session_id:
            increment_session_query_count(session_id)

        # Return successful response
        return {
            "response": query_result.get('answer'),
            "products": products,
            "query_id": query_id
        }

    except HTTPException as http_exc:
        # Log failed query for HTTP exceptions
        response_time_ms = int((time.time() - start_time) * 1000)
        log_query_activity(
            query_text=request.query,
            results_count=0,
            response_time_ms=response_time_ms,
            was_successful=False,
            error_message=f"HTTP {http_exc.status_code}: {http_exc.detail}",
            user_id=current_user.id if current_user else None,
            session_id=session_id,
            selected_stores=request.selected_catalog_ids,
            query_model=request.query_model or 'gemini-2.5-flash-lite'
        )
        # Re-raise HTTPExceptions directly
        raise http_exc
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}\n{traceback.format_exc()}")

        # Log failed query for general exceptions
        response_time_ms = int((time.time() - start_time) * 1000)
        log_query_activity(
            query_text=request.query,
            results_count=0,
            response_time_ms=response_time_ms,
            was_successful=False,
            error_message=str(e),
            user_id=current_user.id if current_user else None,
            session_id=session_id,
            selected_stores=request.selected_catalog_ids,
            query_model=request.query_model or 'gemini-2.5-flash-lite'
        )

        # Return a generic server error in the expected format
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")



@app.post("/api/parse_catalog/{catalog_id}")
async def parse_catalog(
    catalog_id: int,
    background_tasks: BackgroundTasks, # Moved up
    db: Session = Depends(get_db),
    admin_user: schemas.User = Depends(auth.require_admin),
    parsing_model: Optional[str] = Form(None)
):
    """Parse a catalog with Gemini. Now uses background tasks."""
    catalog = db.query(Catalog).filter(Catalog.id == catalog_id).first()
    if not catalog:
        raise HTTPException(status_code=404, detail="Catalog not found")

    if catalog_id in PROCESSING_CATALOGS and PROCESSING_CATALOGS[catalog_id].get("status") == "processing":
        raise HTTPException(status_code=400, detail=f"Catalog {catalog_id} is already being processed.")

    # 🚨 ENTERPRISE FIX: Check if catalog has pages - if not, re-process PDF first
    page_count = db.query(CatalogPage).filter(CatalogPage.catalog_id == catalog_id).count()

    if page_count == 0:
        logger.info(f"Catalog {catalog_id} has no pages - re-processing PDF from cloud storage first")
        PROCESSING_CATALOGS[catalog_id] = {"status": "queued", "message": "Re-processing PDF to create pages."}
        background_tasks.add_task(
            reprocess_catalog_pdf_background,
            catalog_id=catalog_id,
            model_name=parsing_model,
            status_dict=PROCESSING_CATALOGS
        )
    else:
        logger.info(f"Catalog {catalog_id} has {page_count} pages - proceeding with parsing")
        PROCESSING_CATALOGS[catalog_id] = {"status": "queued", "message": "Re-parsing task queued."}
        background_tasks.add_task(
            process_catalog_pages_background,
            catalog_id=catalog_id,
            model_name=parsing_model,
            status_dict=PROCESSING_CATALOGS
        )

    return {
        "success": True,
        "message": f"Re-parsing for catalog {catalog_id} started in background."
    }

@app.post("/api/parse_all_unprocessed")
async def parse_all_unprocessed(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    admin_user: schemas.User = Depends(auth.require_admin),
    parsing_model: Optional[str] = Form(None)
):
    """Parse all catalogs that haven't been processed yet."""
    try:
        # Find catalogs with no products (unprocessed)
        unprocessed_catalogs = db.query(Catalog).outerjoin(Product).filter(
            Product.catalog_id.is_(None)
        ).all()

        if not unprocessed_catalogs:
            return {
                "success": True,
                "message": "No unprocessed catalogs found.",
                "catalogs_queued": 0
            }

        queued_count = 0
        for catalog in unprocessed_catalogs:
            # Skip if already processing
            if catalog.id in PROCESSING_CATALOGS and PROCESSING_CATALOGS[catalog.id].get("status") == "processing":
                continue

            logger.info(f"Queueing catalog {catalog.id} for parsing")
            PROCESSING_CATALOGS[catalog.id] = {"status": "queued", "message": "Bulk parsing task queued."}
            background_tasks.add_task(
                process_catalog_pages_background,
                catalog_id=catalog.id,
                model_name=parsing_model,
                status_dict=PROCESSING_CATALOGS
            )
            queued_count += 1

        return {
            "success": True,
            "message": f"Queued {queued_count} catalogs for parsing.",
            "catalogs_queued": queued_count
        }

    except Exception as e:
        logger.error(f"Error queueing unprocessed catalogs: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error queueing catalogs: {str(e)}")

@app.delete("/catalog/{catalog_id}", status_code=200)
async def delete_catalog(
    catalog_id: int, 
    db: Session = Depends(get_db),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """Delete a catalog, its pages, products, and associated files (PDF and images)."""
    print(f"--- DELETE /catalog/{catalog_id} ENTERED ---") # LOUD LOG 1
    catalog = db.query(Catalog).filter(Catalog.id == catalog_id).first()
    if not catalog:
        print(f"--- DELETE /catalog/{catalog_id} FAILED: Catalog not found ---") # LOUD LOG 2
        raise HTTPException(status_code=404, detail="Catalog not found")

    print(f"--- DELETE /catalog/{catalog_id}: Found catalog {catalog.title} ---") # LOUD LOG 3

    # --- Get paths before deleting DB records --- 
    relative_pdf_path = catalog.pdf_path
    # Get all associated image page records to find the directory
    pages = db.query(CatalogPage).filter(CatalogPage.catalog_id == catalog_id).all()
    relative_image_dir = None
    if pages and pages[0].image_path: # Check if first page has a path
        # Assume all images for a catalog are in the same directory
        relative_image_dir = os.path.dirname(str(pages[0].image_path))
        logger.info(f"Identified relative image directory for deletion: {relative_image_dir}")

    # Cloud storage paths are already stored in the database
    # No need to construct full paths - we use cloud storage directly

    try:
        # --- Delete DB Records First (in correct order to avoid foreign key violations) ---
        print(f"--- DELETE /catalog/{catalog_id}: Deleting processing queue entries... ---") # LOUD LOG 4
        queue_count = db.query(CatalogProcessingQueue).filter(CatalogProcessingQueue.catalog_db_id == catalog_id).delete()
        logger.info(f"Deleted {queue_count} processing queue entries for catalog {catalog_id}")

        print(f"--- DELETE /catalog/{catalog_id}: Deleting processed catalog hashes... ---") # LOUD LOG 5
        hash_count = db.query(ProcessedCatalogHashes).filter(ProcessedCatalogHashes.catalog_db_id == catalog_id).delete()
        logger.info(f"Deleted {hash_count} processed catalog hash entries for catalog {catalog_id}")

        print(f"--- DELETE /catalog/{catalog_id}: Deleting products... ---") # LOUD LOG 6
        product_count = db.query(Product).filter(Product.catalog_id == catalog_id).delete()
        logger.info(f"Deleted {product_count} products for catalog {catalog_id}")

        print(f"--- DELETE /catalog/{catalog_id}: Deleting pages... ---") # LOUD LOG 7
        page_count = db.query(CatalogPage).filter(CatalogPage.catalog_id == catalog_id).delete()
        logger.info(f"Deleted {page_count} pages for catalog {catalog_id}")

        print(f"--- DELETE /catalog/{catalog_id}: Deleting catalog object... ---") # LOUD LOG 8
        db.delete(catalog)
        
        print(f"--- DELETE /catalog/{catalog_id}: **BEFORE COMMIT** ---") # LOUD LOG 9
        db.commit()
        print(f"--- DELETE /catalog/{catalog_id}: **AFTER COMMIT** ---") # LOUD LOG 10

        logger.info(f"Deleted catalog record {catalog_id} from database")

        # --- Delete Cloud Storage Files ---
        pdf_deleted = False
        images_deleted = 0

        # Delete PDF file from cloud storage
        if relative_pdf_path:
            try:
                if cloud_storage.delete_file(str(relative_pdf_path)):
                    logger.info(f"Deleted PDF from cloud storage: {relative_pdf_path}")
                    pdf_deleted = True
                else:
                    logger.warning(f"Failed to delete PDF from cloud storage: {relative_pdf_path}")
            except Exception as e:
                logger.error(f"Error deleting PDF from cloud storage {relative_pdf_path}: {e}")

        # Delete individual page images from cloud storage
        for page in pages:
            if page.image_path:
                try:
                    if cloud_storage.delete_file(str(page.image_path)):
                        logger.info(f"Deleted image from cloud storage: {page.image_path}")
                        images_deleted += 1
                    else:
                        logger.warning(f"Failed to delete image from cloud storage: {page.image_path}")
                except Exception as e:
                    logger.error(f"Error deleting image from cloud storage {page.image_path}: {e}")

        return {"message": f"Catalog {catalog_id} deleted successfully (PDF Deleted: {pdf_deleted}, Images Deleted: {images_deleted})"}

    except Exception as e:
        db.rollback() # Rollback DB changes if anything failed during DB operations
        # LOUDER EXCEPTION LOGGING
        print(f"--- !! DELETE /catalog/{catalog_id} ERROR in try block !! ---")
        print(f"Exception Type: {type(e)}")
        print(f"Exception Args: {e.args}")
        print(f"Exception Details: {e}")
        logger.error(f"Error deleting catalog {catalog_id} during DB operation: {e}", exc_info=True) # Keep logger too
        # Don't delete files if DB operation failed before commit
        raise HTTPException(status_code=500, detail=f"Error deleting catalog DB records: {str(e)}")

@app.get("/models", response_model=List[str])
async def list_models():
    """List available generative models, handling potential API key issues."""
    api_key_present = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
    if not api_key_present:
        logger.warning("No API key found (GOOGLE_API_KEY or GEMINI_API_KEY) when trying to list models.")
        return []

    try:
        # Initialize client with new unified SDK
        client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

        supported_models = []
        for m in client.models.list():
            # We only want models that can be used for content generation.
            if hasattr(m, 'supported_generation_methods') and 'generateContent' in m.supported_generation_methods:
                supported_models.append(m.name)
            else:
                # For new SDK, assume all listed models support content generation
                supported_models.append(m.name)
        logger.info(f"Found {len(supported_models)} supported Gemini models.")
        return supported_models
    except Exception as e:
        logger.error(f"Error listing Gemini models: {e}", exc_info=True)
        return []

# --- Root endpoint for health check ---
@app.get("/", summary="API Health Check")
def health_check():
    """Provides a simple health check endpoint to confirm the API is running."""
    return {"status": "ok", "message": "Danish Supermarket Offers API is running."}

# --- NEW: Settings API Routes for React Frontend ---
from pydantic import BaseModel

class SettingsPayload(BaseModel):
    # Existing settings
    parsing_model: Optional[str] = None
    query_model: Optional[str] = None
    query_system_prompt: Optional[str] = None
    parser_system_prompt: Optional[str] = None
    initial_rate: Optional[str] = None
    max_rate: Optional[str] = None
    min_rate: Optional[str] = None
    show_ai_debug_output: Optional[str] = None

    # New Google GenAI settings
    # Core generation parameters
    temperature: Optional[str] = None
    top_p: Optional[str] = None
    top_k: Optional[str] = None
    max_output_tokens: Optional[str] = None
    candidate_count: Optional[str] = None
    seed: Optional[str] = None
    stop_sequences: Optional[str] = None  # JSON string of array
    presence_penalty: Optional[str] = None
    frequency_penalty: Optional[str] = None

    # Safety settings (4 categories)
    safety_hate_speech: Optional[str] = None
    safety_dangerous_content: Optional[str] = None
    safety_harassment: Optional[str] = None
    safety_sexually_explicit: Optional[str] = None

    # Advanced features
    grounding_with_google_search: Optional[str] = None
    response_mime_type: Optional[str] = None

    # Query processing limits
    max_context_length: Optional[str] = None
    max_products_per_category: Optional[str] = None
    max_products_returned: Optional[str] = None

@app.get("/api/settings", response_model=Dict[str, Any])
async def get_settings_api(admin_user: schemas.User = Depends(auth.require_admin)):
    """Provides current application settings as a JSON object for the frontend."""
    logger.info("Fetching settings for admin UI.")

    # Use the new database-first configuration system
    from config import get_setting_from_db, get_float_setting_from_db, get_int_setting_from_db, get_bool_setting_from_db

    # Map backend config keys to frontend state keys
    frontend_settings = {
        # Existing settings
        "current_parsing_model": get_setting_from_db("GEMINI_PARSING_MODEL", "models/gemini-1.5-flash-latest"),
        "current_query_model": get_setting_from_db("GEMINI_QUERY_MODEL", "models/gemini-1.5-flash-latest"),
        "current_parser_system_prompt": get_setting_from_db("GEMINI_PARSER_SYSTEM_PROMPT", ""),
        "current_query_system_prompt": get_setting_from_db("GEMINI_QUERY_SYSTEM_PROMPT", ""),
        "current_initial_rate": get_setting_from_db("API_INITIAL_RATE", "10"),
        "current_max_rate": get_setting_from_db("API_MAX_RATE", "30"),
        "current_min_rate": get_setting_from_db("API_MIN_RATE", "1"),
        "show_ai_debug_output": get_bool_setting_from_db("AI_DEBUG_MODE", False),

        # New Google GenAI settings with defaults
        "temperature": str(get_float_setting_from_db("GEMINI_TEMPERATURE", 0.7)),
        "top_p": str(get_float_setting_from_db("GEMINI_TOP_P", 0.95)),
        "top_k": str(get_int_setting_from_db("GEMINI_TOP_K", 20)),
        "max_output_tokens": str(get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048)),
        "candidate_count": str(get_int_setting_from_db("GEMINI_CANDIDATE_COUNT", 1)),
        "seed": get_setting_from_db("GEMINI_SEED", ""),
        "stop_sequences": get_setting_from_db("GEMINI_STOP_SEQUENCES", "[]"),
        "presence_penalty": str(get_float_setting_from_db("GEMINI_PRESENCE_PENALTY", 0.0)),
        "frequency_penalty": str(get_float_setting_from_db("GEMINI_FREQUENCY_PENALTY", 0.0)),

        # Safety settings
        "safety_hate_speech": get_setting_from_db("GEMINI_SAFETY_HATE_SPEECH", "BLOCK_ONLY_HIGH"),
        "safety_dangerous_content": get_setting_from_db("GEMINI_SAFETY_DANGEROUS_CONTENT", "BLOCK_ONLY_HIGH"),
        "safety_harassment": get_setting_from_db("GEMINI_SAFETY_HARASSMENT", "BLOCK_ONLY_HIGH"),
        "safety_sexually_explicit": get_setting_from_db("GEMINI_SAFETY_SEXUALLY_EXPLICIT", "BLOCK_ONLY_HIGH"),

        # Advanced features
        "grounding_with_google_search": get_setting_from_db("GEMINI_GROUNDING_WITH_GOOGLE_SEARCH", "false"),
        "response_mime_type": get_setting_from_db("GEMINI_RESPONSE_MIME_TYPE", "text/plain"),

        # Query processing limits
        "max_context_length": get_int_setting_from_db("QUERY_MAX_CONTEXT_LENGTH", 800000),
        "max_products_per_category": get_int_setting_from_db("QUERY_MAX_PRODUCTS_PER_CATEGORY", 100),
        "max_products_returned": get_int_setting_from_db("QUERY_MAX_PRODUCTS_RETURNED", 12),
    }
    return frontend_settings

@app.post("/api/settings")
async def post_settings_api(payload: SettingsPayload, db: Session = Depends(get_db), admin_user: schemas.User = Depends(auth.require_admin)):
    """
    Handles saving updated settings from the React admin page.
    It accepts a JSON payload, saves each key-value pair to the database,
    and then triggers a hot-reload of the application's configuration.
    """
    logger.info(f"Admin '{admin_user.username}' is updating settings via API.")
    
    # Map frontend payload keys to backend database keys
    settings_to_update = {
        # Existing settings
        "GEMINI_PARSING_MODEL": payload.parsing_model,
        "GEMINI_QUERY_MODEL": payload.query_model,
        "GEMINI_PARSER_SYSTEM_PROMPT": payload.parser_system_prompt,
        "GEMINI_QUERY_SYSTEM_PROMPT": payload.query_system_prompt,
        "API_INITIAL_RATE": payload.initial_rate,
        "API_MAX_RATE": payload.max_rate,
        "API_MIN_RATE": payload.min_rate,
        "AI_DEBUG_MODE": payload.show_ai_debug_output,

        # New Google GenAI settings
        "GEMINI_TEMPERATURE": payload.temperature,
        "GEMINI_TOP_P": payload.top_p,
        "GEMINI_TOP_K": payload.top_k,
        "GEMINI_MAX_OUTPUT_TOKENS": payload.max_output_tokens,
        "GEMINI_CANDIDATE_COUNT": payload.candidate_count,
        "GEMINI_SEED": payload.seed,
        "GEMINI_STOP_SEQUENCES": payload.stop_sequences,
        "GEMINI_PRESENCE_PENALTY": payload.presence_penalty,
        "GEMINI_FREQUENCY_PENALTY": payload.frequency_penalty,

        # Safety settings
        "GEMINI_SAFETY_HATE_SPEECH": payload.safety_hate_speech,
        "GEMINI_SAFETY_DANGEROUS_CONTENT": payload.safety_dangerous_content,
        "GEMINI_SAFETY_HARASSMENT": payload.safety_harassment,
        "GEMINI_SAFETY_SEXUALLY_EXPLICIT": payload.safety_sexually_explicit,

        # Advanced features
        "GEMINI_GROUNDING_WITH_GOOGLE_SEARCH": payload.grounding_with_google_search,
        "GEMINI_RESPONSE_MIME_TYPE": payload.response_mime_type,

        # Query processing limits
        "QUERY_MAX_CONTEXT_LENGTH": payload.max_context_length,
        "QUERY_MAX_PRODUCTS_PER_CATEGORY": payload.max_products_per_category,
        "QUERY_MAX_PRODUCTS_RETURNED": payload.max_products_returned,
    }

    for key, value in settings_to_update.items():
        if value is not None:
            logger.info(f"Updating setting: '{key}'")
            set_setting(db, key, str(value)) # Ensure value is string for DB

    db.commit()
    # No need to reload config - database-first system fetches fresh values automatically
    logger.info("Application settings have been updated in the database.")

    return {"message": "Settings updated successfully!"}


@app.get("/api/settings/effective", response_model=Dict[str, Any])
async def get_effective_settings_api(admin_user: schemas.User = Depends(auth.require_admin)):
    """
    Returns the actual effective settings as used by the backend.
    This shows the resolved values with fallbacks, exactly as the backend sees them.
    """
    logger.info("Fetching effective settings for verification.")

    # Import here to avoid circular imports
    from config import get_setting_from_db, get_float_setting_from_db, get_int_setting_from_db, get_bool_setting_from_db

    # Get all the settings exactly as the backend uses them
    effective_settings = {
        # Core model settings
        "GEMINI_PARSING_MODEL": get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest'),
        "GEMINI_QUERY_MODEL": get_setting_from_db('GEMINI_QUERY_MODEL', 'models/gemini-1.5-flash-latest'),

        # System prompts
        "GEMINI_PARSER_SYSTEM_PROMPT": get_setting_from_db('GEMINI_PARSER_SYSTEM_PROMPT', ''),
        "GEMINI_QUERY_SYSTEM_PROMPT": get_setting_from_db('GEMINI_QUERY_SYSTEM_PROMPT', ''),

        # Generation parameters
        "GEMINI_TEMPERATURE": get_float_setting_from_db("GEMINI_TEMPERATURE", 0.7),
        "GEMINI_TOP_P": get_float_setting_from_db("GEMINI_TOP_P", 0.95),
        "GEMINI_TOP_K": get_int_setting_from_db("GEMINI_TOP_K", 20),
        "GEMINI_MAX_OUTPUT_TOKENS": get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048),
        "GEMINI_CANDIDATE_COUNT": get_int_setting_from_db("GEMINI_CANDIDATE_COUNT", 1),
        "GEMINI_SEED": get_setting_from_db("GEMINI_SEED", ""),
        "GEMINI_STOP_SEQUENCES": get_setting_from_db("GEMINI_STOP_SEQUENCES", "[]"),
        "GEMINI_PRESENCE_PENALTY": get_float_setting_from_db("GEMINI_PRESENCE_PENALTY", 0.0),
        "GEMINI_FREQUENCY_PENALTY": get_float_setting_from_db("GEMINI_FREQUENCY_PENALTY", 0.0),

        # Safety settings
        "GEMINI_SAFETY_HATE_SPEECH": get_setting_from_db("GEMINI_SAFETY_HATE_SPEECH", "BLOCK_ONLY_HIGH"),
        "GEMINI_SAFETY_DANGEROUS_CONTENT": get_setting_from_db("GEMINI_SAFETY_DANGEROUS_CONTENT", "BLOCK_ONLY_HIGH"),
        "GEMINI_SAFETY_HARASSMENT": get_setting_from_db("GEMINI_SAFETY_HARASSMENT", "BLOCK_ONLY_HIGH"),
        "GEMINI_SAFETY_SEXUALLY_EXPLICIT": get_setting_from_db("GEMINI_SAFETY_SEXUALLY_EXPLICIT", "BLOCK_ONLY_HIGH"),

        # API settings
        "GEMINI_API_DELAY_SECONDS": get_int_setting_from_db('GEMINI_API_DELAY_SECONDS', 2),
        "PARSER_MAX_RETRIES": get_int_setting_from_db('PARSER_MAX_RETRIES', 3),

        # Rate limiting
        "API_INITIAL_RATE": get_setting_from_db("API_INITIAL_RATE", "10"),
        "API_MAX_RATE": get_setting_from_db("API_MAX_RATE", "30"),
        "API_MIN_RATE": get_setting_from_db("API_MIN_RATE", "1"),

        # Debug settings
        "AI_DEBUG_MODE": get_bool_setting_from_db("AI_DEBUG_MODE", False),

        # Advanced features
        "GEMINI_GROUNDING_WITH_GOOGLE_SEARCH": get_setting_from_db("GEMINI_GROUNDING_WITH_GOOGLE_SEARCH", "false"),
        "GEMINI_RESPONSE_MIME_TYPE": get_setting_from_db("GEMINI_RESPONSE_MIME_TYPE", "text/plain"),

        # Query processing limits
        "QUERY_MAX_CONTEXT_LENGTH": get_int_setting_from_db("QUERY_MAX_CONTEXT_LENGTH", 800000),
        "QUERY_MAX_PRODUCTS_PER_CATEGORY": get_int_setting_from_db("QUERY_MAX_PRODUCTS_PER_CATEGORY", 100),
        "QUERY_MAX_PRODUCTS_RETURNED": get_int_setting_from_db("QUERY_MAX_PRODUCTS_RETURNED", 12),
    }

    # Add metadata about when this was fetched
    effective_settings["_metadata"] = {
        "fetched_at": datetime.now().isoformat(),
        "fetched_by": admin_user.username,
        "note": "These are the actual values used by the backend with fallbacks applied"
    }

    return effective_settings


@app.post("/api/settings/test")
async def test_settings_usage(admin_user: schemas.User = Depends(auth.require_admin)):
    """
    Test endpoint that actually uses the settings and reports what was used.
    This simulates a real backend operation to verify settings are working.
    """
    logger.info("Testing settings usage...")

    from config import get_setting_from_db, get_float_setting_from_db, get_int_setting_from_db

    # Simulate what the backend actually does when processing
    test_results = {
        "test_timestamp": datetime.now().isoformat(),
        "test_performed_by": admin_user.username,
        "settings_used": {},
        "operations_simulated": []
    }

    try:
        # Test 1: Model selection (as used in gemini_parser.py)
        parsing_model = get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest')
        test_results["settings_used"]["parsing_model"] = parsing_model
        test_results["operations_simulated"].append(f"✅ Parsing model resolved to: {parsing_model}")

        # Test 2: Generation parameters (as used in gemini_parser.py)
        temperature = get_float_setting_from_db("GEMINI_TEMPERATURE", 0.7)
        top_p = get_float_setting_from_db("GEMINI_TOP_P", 0.95)
        max_tokens = get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048)

        test_results["settings_used"]["temperature"] = temperature
        test_results["settings_used"]["top_p"] = top_p
        test_results["settings_used"]["max_tokens"] = max_tokens

        test_results["operations_simulated"].append(f"✅ Generation config: temp={temperature}, top_p={top_p}, max_tokens={max_tokens}")

        # Test 3: Query model (as used in query_processor.py)
        query_model = get_setting_from_db('GEMINI_QUERY_MODEL', 'models/gemini-1.5-flash-latest')
        test_results["settings_used"]["query_model"] = query_model
        test_results["operations_simulated"].append(f"✅ Query model resolved to: {query_model}")

        # Test 4: System prompts
        parser_prompt = get_setting_from_db('GEMINI_PARSER_SYSTEM_PROMPT', '')
        query_prompt = get_setting_from_db('GEMINI_QUERY_SYSTEM_PROMPT', '')

        test_results["settings_used"]["parser_prompt_length"] = len(parser_prompt) if parser_prompt else 0
        test_results["settings_used"]["query_prompt_length"] = len(query_prompt) if query_prompt else 0

        test_results["operations_simulated"].append(f"✅ Parser prompt: {len(parser_prompt) if parser_prompt else 0} characters")
        test_results["operations_simulated"].append(f"✅ Query prompt: {len(query_prompt) if query_prompt else 0} characters")

        # Test 5: API delay (as used in gemini_parser.py)
        api_delay = get_int_setting_from_db('GEMINI_API_DELAY_SECONDS', 2)
        test_results["settings_used"]["api_delay"] = api_delay
        test_results["operations_simulated"].append(f"✅ API delay setting: {api_delay} seconds")

        test_results["test_status"] = "SUCCESS"
        test_results["summary"] = f"All {len(test_results['operations_simulated'])} setting tests passed. Backend is using database settings correctly."

    except Exception as e:
        test_results["test_status"] = "ERROR"
        test_results["error"] = str(e)
        test_results["summary"] = f"Settings test failed: {str(e)}"
        logger.error(f"Settings test failed: {e}", exc_info=True)

    return test_results


@app.post("/api/admin/test-parser")
async def test_parser_endpoint(
    file: UploadFile = File(...),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """
    Parser testing playground for admin users.
    Upload an image and test the current parser settings in real-time.
    """
    logger.info(f"Parser test initiated by admin: {admin_user.username}")

    # Validate file type
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    # Save uploaded file temporarily
    import tempfile
    import os
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    temp_filename = f"parser_test_{timestamp}_{file.filename}"
    temp_path = os.path.join(tempfile.gettempdir(), temp_filename)

    try:
        # Save uploaded file
        with open(temp_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        logger.info(f"Test image saved to: {temp_path}")

        # Import parser function
        from gemini_parser import parse_file_with_gemini

        # Get current settings for display
        from config import get_setting_from_db, get_int_setting_from_db, get_float_setting_from_db

        current_settings = {
            "model": get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest'),
            "temperature": get_float_setting_from_db("GEMINI_TEMPERATURE", 0.7),
            "max_tokens": get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048),
            "max_retries": get_int_setting_from_db('PARSER_MAX_RETRIES', 3),
        }

        # Record start time
        start_time = datetime.now()

        # Run the parser with current settings
        try:
            parsed_products = parse_file_with_gemini(
                temp_path,
                model_name=None  # Use current settings
            )

            parsing_success = True
            error_message = None

        except Exception as e:
            parsed_products = []
            parsing_success = False
            error_message = str(e)
            logger.error(f"Parser test failed: {e}", exc_info=True)

        # Calculate processing time
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        # Prepare response
        response = {
            "test_metadata": {
                "timestamp": start_time.isoformat(),
                "filename": file.filename,
                "tested_by": admin_user.username,
                "processing_time_seconds": round(processing_time, 2),
                "parsing_success": parsing_success,
                "error_message": error_message
            },
            "current_settings": current_settings,
            "results": {
                "products_found": len(parsed_products),
                "products": parsed_products
            }
        }

        logger.info(f"Parser test completed: {len(parsed_products)} products found in {processing_time:.2f}s")
        return response

    except Exception as e:
        logger.error(f"Parser test endpoint failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Parser test failed: {str(e)}")

    finally:
        # Clean up temporary file
        try:
            if os.path.exists(temp_path):
                os.remove(temp_path)
                logger.debug(f"Cleaned up temp file: {temp_path}")
        except Exception as e:
            logger.warning(f"Failed to clean up temp file {temp_path}: {e}")


@app.get("/api/admin/catalog-pages")
async def get_catalog_pages_for_testing(
    catalog_id: Optional[int] = None,
    limit: int = 20,
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """
    Get catalog pages for parser testing.
    Returns a list of pages that can be used for testing the parser.
    """
    logger.info(f"Fetching catalog pages for parser testing by: {admin_user.username}")

    try:
        with get_db_session() as session:
            query = session.query(models.CatalogPage)

            if catalog_id:
                query = query.filter(models.CatalogPage.catalog_id == catalog_id)

            # Get pages with their catalog info
            query = query.join(models.Catalog).order_by(
                models.Catalog.id.desc(),
                models.CatalogPage.page_number
            ).limit(limit)

            pages = query.all()

            result = []
            for page in pages:
                result.append({
                    "page_id": page.id,
                    "catalog_id": page.catalog_id,
                    "catalog_name": page.catalog.name if page.catalog else "Unknown",
                    "store_name": page.catalog.store.name if page.catalog and page.catalog.store else "Unknown",
                    "page_number": page.page_number,
                    "cloud_path": page.cloud_path,
                    "processed": page.processed,
                    "product_count": len(page.products) if page.products else 0
                })

            logger.info(f"Found {len(result)} catalog pages for testing")
            return {"pages": result}

    except Exception as e:
        logger.error(f"Failed to fetch catalog pages: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to fetch catalog pages: {str(e)}")


@app.post("/api/admin/test-parser-existing-page/{page_id}")
async def test_parser_existing_page(
    page_id: int,
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """
    Test parser on an existing catalog page.
    Downloads the page from cloud storage and runs parser with current settings.
    """
    logger.info(f"Testing parser on existing page {page_id} by admin: {admin_user.username}")

    try:
        with get_db_session() as session:
            page = session.query(models.CatalogPage).filter(models.CatalogPage.id == page_id).first()

            if not page:
                raise HTTPException(status_code=404, detail="Catalog page not found")

            if not page.cloud_path:
                raise HTTPException(status_code=400, detail="Page has no cloud path")

            # Download from cloud storage
            from cloud_storage import download_file_from_cloud
            import tempfile
            import os
            from datetime import datetime

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"parser_test_page_{page_id}_{timestamp}.png"
            temp_path = os.path.join(tempfile.gettempdir(), temp_filename)

            try:
                # Download the file
                success = download_file_from_cloud(page.cloud_path, temp_path)
                if not success:
                    raise HTTPException(status_code=500, detail="Failed to download page from cloud storage")

                # Import parser function
                from gemini_parser import parse_file_with_gemini

                # Get current settings
                from config import get_setting_from_db, get_int_setting_from_db, get_float_setting_from_db

                current_settings = {
                    "model": get_setting_from_db('GEMINI_PARSING_MODEL', 'models/gemini-1.5-flash-latest'),
                    "temperature": get_float_setting_from_db("GEMINI_TEMPERATURE", 0.7),
                    "max_tokens": get_int_setting_from_db("GEMINI_MAX_OUTPUT_TOKENS", 2048),
                    "max_retries": get_int_setting_from_db('PARSER_MAX_RETRIES', 3),
                }

                # Record start time
                start_time = datetime.now()

                # Run the parser
                try:
                    parsed_products = parse_file_with_gemini(
                        temp_path,
                        model_name=None  # Use current settings
                    )

                    parsing_success = True
                    error_message = None

                except Exception as e:
                    parsed_products = []
                    parsing_success = False
                    error_message = str(e)
                    logger.error(f"Parser test failed for page {page_id}: {e}", exc_info=True)

                # Calculate processing time
                end_time = datetime.now()
                processing_time = (end_time - start_time).total_seconds()

                # Get existing products for comparison
                existing_products = []
                for product in page.products:
                    existing_products.append({
                        "id": product.id,
                        "name": product.name,
                        "price": float(product.price) if product.price else None,
                        "unit": product.unit,
                        "category": product.category
                    })

                # Prepare response
                response = {
                    "test_metadata": {
                        "timestamp": start_time.isoformat(),
                        "page_id": page_id,
                        "catalog_name": page.catalog.name if page.catalog else "Unknown",
                        "store_name": page.catalog.store.name if page.catalog and page.catalog.store else "Unknown",
                        "page_number": page.page_number,
                        "tested_by": admin_user.username,
                        "processing_time_seconds": round(processing_time, 2),
                        "parsing_success": parsing_success,
                        "error_message": error_message
                    },
                    "current_settings": current_settings,
                    "results": {
                        "new_products_found": len(parsed_products),
                        "existing_products_count": len(existing_products),
                        "new_products": parsed_products,
                        "existing_products": existing_products
                    }
                }

                logger.info(f"Parser test completed for page {page_id}: {len(parsed_products)} new products vs {len(existing_products)} existing")
                return response

            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                        logger.debug(f"Cleaned up temp file: {temp_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temp file {temp_path}: {e}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Parser test endpoint failed for page {page_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Parser test failed: {str(e)}")


# --- Internal endpoints for Render cron triggers ---
from starlette.requests import Request

@app.post("/internal/run_catalog_processor")
async def trigger_catalog_processor(request: Request):
    secret = request.headers.get("x-cron-secret")
    if secret != os.getenv("CRON_SECRET"):
        raise HTTPException(status_code=403, detail="Forbidden")
    subprocess.Popen(["python", "catalog_processor.py"])
    return {"status": "catalog_processor_started"}

@app.post("/internal/make_bucket_public")
async def make_bucket_public_endpoint(request: Request):
    """
    Internal endpoint to make the Google Cloud Storage bucket publicly readable.
    This is a one-time setup that makes ALL files (current and future) public.
    Protected by cron secret for security.
    """
    secret = request.headers.get("x-cron-secret")
    # Handle URL-encoded secret
    import urllib.parse
    if secret:
        secret = urllib.parse.unquote(secret)
    if secret != os.getenv("CRON_SECRET"):
        raise HTTPException(status_code=403, detail="Forbidden")

    logger.info("🚀 Making Google Cloud Storage bucket publicly readable...")

    try:
        if not cloud_storage.is_available():
            logger.error("Cloud storage not available")
            raise HTTPException(status_code=500, detail="Cloud storage not available")

        # Get the current IAM policy
        policy = cloud_storage.bucket.get_iam_policy(requested_policy_version=3)

        # Add the public read binding
        # This gives all users (allUsers) the Storage Object Viewer role
        policy.bindings.append({
            "role": "roles/storage.objectViewer",
            "members": {"allUsers"}
        })

        # Set the updated policy
        cloud_storage.bucket.set_iam_policy(policy)

        logger.info("✅ Successfully set bucket IAM policy for public read access!")
        logger.info("🌐 All files in the bucket are now publicly accessible")
        logger.info("🚀 Future uploads will also be automatically public")

        return {
            "status": "success",
            "message": "Bucket is now publicly readable",
            "details": "All current and future files are automatically public"
        }

    except Exception as e:
        logger.error(f"❌ Failed to set bucket IAM policy: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to make bucket public: {str(e)}")

@app.get("/internal/test_bucket_public")
async def test_bucket_public():
    """
    Test endpoint to verify if bucket files are publicly accessible.
    No authentication required since this is just testing public URLs.
    """
    import requests

    test_urls = [
        "https://storage.googleapis.com/tilbudsjaegeren/logos/supermarkets/meny.png",
        "https://storage.googleapis.com/tilbudsjaegeren/logos/supermarkets/netto.svg",
        "https://storage.googleapis.com/tilbudsjaegeren/images/catalog_42/page_1.png"
    ]

    results = []

    for url in test_urls:
        try:
            response = requests.head(url, timeout=10)
            results.append({
                "url": url,
                "status_code": response.status_code,
                "accessible": response.status_code == 200
            })
        except Exception as e:
            results.append({
                "url": url,
                "status_code": None,
                "accessible": False,
                "error": str(e)
            })

    all_accessible = all(result["accessible"] for result in results)

    return {
        "bucket_public": all_accessible,
        "test_results": results,
        "message": "All files are publicly accessible!" if all_accessible else "Some files are not publicly accessible"
    }

# --- Legacy endpoints for backward compatibility ---
@app.post("/trigger-catalog-processor")
async def legacy_trigger_catalog_processor(request: Request):
    """Legacy endpoint that redirects to the internal endpoint"""
    logger.warning("Legacy endpoint /trigger-catalog-processor called, redirecting to /internal/run_catalog_processor")
    return await trigger_catalog_processor(request)

# --- Temporary diagnostic endpoint ---
@app.get("/debug/render-environment")
async def debug_render_environment():
    """Temporary endpoint to diagnose Render environment issues"""
    try:
        result = subprocess.run(
            ["python", "debug_render_environment.py"],
            capture_output=True,
            text=True,
            timeout=60
        )

        return {
            "status": "success",
            "return_code": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    except subprocess.TimeoutExpired:
        return {
            "status": "timeout",
            "error": "Diagnostic script timed out after 60 seconds"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

# --- Authentication Endpoints ---

@app.post("/logout")
async def logout():
    """Remove auth cookie so browser is logged out server-side."""
    response = JSONResponse(content={"detail": "logged out"})
    response.delete_cookie("access_token_cookie")
    return response

from fastapi.responses import JSONResponse

@app.post("/token", response_model=schemas.Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = auth.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = auth.create_access_token(data={"sub": user.username})
    refresh_token = auth.create_refresh_token(data={"sub": user.username})

    # Set HttpOnly cookie so server-rendered pages know the user immediately
    response = JSONResponse(
        content={
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
        }
    )
    response.set_cookie(
        key="access_token_cookie",  # Keep name in sync with auth.get_current_user
        value=access_token,
        max_age=60 * 60 * 24 * 7,  # 1 week
        httponly=True,
        secure=True,
        samesite="lax",
    )
    return response

@app.post("/auth/refresh_token", response_model=schemas.Token)
async def refresh_token(request: Request, db: Session = Depends(get_db)):
    try:
        # The frontend sends the refresh token in the Authorization header
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Invalid refresh token format")
        
        token = auth_header.split(" ")[1]
        
        user = auth.verify_refresh_token(token, db)
        if not user:
            # This case is handled inside verify_refresh_token, but as a fallback
            raise HTTPException(status_code=401, detail="Invalid refresh token")

        # Issue a new access token and return both (the refresh token can be reused until it expires)
        new_access_token = auth.create_access_token(data={"sub": user.username})
        
        return {
            "access_token": new_access_token,
            "refresh_token": token, # Return the same refresh token
            "token_type": "bearer"
        }
    except HTTPException as e:
        # Re-raise HTTP exceptions to ensure correct status codes are sent
        raise e
    except Exception as e:
        logger.error(f"Error during token refresh: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error during token refresh")
    return response

@app.post("/validate-password")
def validate_password_endpoint(password_data: dict):
    """Validate password strength for real-time feedback"""
    password = password_data.get("password", "")
    is_valid, error_message = auth.validate_password(password)
    return {
        "valid": is_valid,
        "message": error_message if not is_valid else "Password is strong",
        "requirements": {
            "min_length": len(password) >= 8,
            "has_letter": any(c.isalpha() for c in password),
            "has_number": any(c.isdigit() for c in password)
        }
    }

@app.post("/users/", response_model=schemas.User)
def create_user_endpoint(user: schemas.UserCreate, db: Session = Depends(get_db)):
    db_user = auth.get_user(db, username=user.username)
    if db_user:
        raise HTTPException(status_code=400, detail="Username already registered")
    return auth.create_user(db=db, user=user)

@app.get("/users/me", response_model=schemas.User)
async def read_users_me(current_user: schemas.User = Depends(auth.get_current_user)):
    return current_user

class UserPreferences(BaseModel):
    preferred_model: Optional[str] = None
    # Add query history update logic if needed separately

@app.put("/users/me/preferences", response_model=schemas.User)
async def update_user_preferences(
    preferences: UserPreferences,
    db: Session = Depends(get_db),
    current_user: schemas.User = Depends(auth.get_current_user)
):
    db_user = db.query(User).filter(User.id == current_user.id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found") # Should not happen if token is valid
    
    updated = False
    if preferences.preferred_model is not None:
        db_user.preferred_model = preferences.preferred_model
        updated = True
        
    # How to handle query history? Append or replace?
    # For now, let's assume query history is updated during the actual query process
    # If you want to set it here, add logic:
    # if preferences.query_history is not None:
    #     db_user.query_history = preferences.query_history # Or process it
    #     updated = True
        
    if updated:
        db.commit()
        db.refresh(db_user)
        
    return db_user

# --- NEW Endpoint to stop parsing ---
@app.post("/stop_parsing_catalog/{catalog_id}", status_code=200)
async def stop_parsing_catalog(catalog_id: int, admin_user: schemas.User = Depends(auth.require_admin)):
    if catalog_id not in PROCESSING_CATALOGS:
        raise HTTPException(status_code=404, detail=f"No active or queued parsing task found for catalog ID {catalog_id}.")
    
    current_status = PROCESSING_CATALOGS[catalog_id].get("status")
    if current_status == "processing" or current_status == "queued":
        PROCESSING_CATALOGS[catalog_id]["status"] = "cancelling"
        PROCESSING_CATALOGS[catalog_id]["message"] = "Cancellation request received."
        logger.info(f"Cancellation requested for catalog ID {catalog_id}.")
        return {"message": f"Cancellation request sent for catalog ID {catalog_id}."}
    elif current_status == "cancelling":
        return {"message": f"Catalog {catalog_id} is already being cancelled."}
    elif current_status == "completed" or current_status == "cancelled" or current_status == "error" or current_status == "skipped":
        return {"message": f"Parsing for catalog {catalog_id} is already {current_status} and cannot be stopped."}
    else:
        raise HTTPException(status_code=400, detail=f"Cannot stop parsing for catalog {catalog_id} in its current state: {current_status}.")

# --- NEW Endpoint to get parsing status ---
@app.get("/parsing_status", response_model=Dict[int, Dict[str, Any]])
async def get_all_parsing_statuses():
    """
    High-frequency endpoint that combines in-memory processing status with database status.
    Optimized with self-managed database session to prevent connection pool exhaustion.
    """
    try:
        # Start with in-memory processing statuses
        result = dict(PROCESSING_CATALOGS)

        # Use self-managed database session for immediate connection release
        with SessionLocal() as db:
            # Get all catalogs to check their database status
            catalogs = db.query(Catalog).all()

            for catalog in catalogs:
                catalog_id = catalog.id

                # Skip if already in memory (current/recent processing)
                if catalog_id in result:
                    continue

                # Check ProcessedCatalogHashes for historical status
                hash_entry = db.query(ProcessedCatalogHashes).filter(
                    ProcessedCatalogHashes.catalog_db_id == catalog_id
                ).order_by(ProcessedCatalogHashes.processed_at.desc()).first()

                # Check CatalogProcessingQueue for queue status
                queue_entry = db.query(CatalogProcessingQueue).filter(
                    CatalogProcessingQueue.catalog_db_id == catalog_id
                ).order_by(CatalogProcessingQueue.added_at.desc()).first()

                # Determine status based on database records
                if hash_entry:
                    if hash_entry.status == 'SUCCESS':
                        result[catalog_id] = {
                            "status": "completed",
                            "message": f"Processing completed successfully on {hash_entry.processed_at.strftime('%Y-%m-%d %H:%M')}"
                        }
                    elif hash_entry.status == 'FAILED':
                        result[catalog_id] = {
                            "status": "error",
                            "message": f"Processing failed on {hash_entry.processed_at.strftime('%Y-%m-%d %H:%M')}"
                        }
                    elif hash_entry.status == 'PENDING':
                        result[catalog_id] = {
                            "status": "queued",
                            "message": "Queued for processing"
                        }
                elif queue_entry:
                    if queue_entry.status == 'PENDING':
                        result[catalog_id] = {
                            "status": "queued",
                            "message": "In processing queue"
                        }
                    elif queue_entry.status == 'PROCESSING':
                        result[catalog_id] = {
                            "status": "processing",
                            "message": "Currently processing"
                        }
                    elif queue_entry.status == 'SUCCESS':
                        result[catalog_id] = {
                            "status": "completed",
                            "message": "Processing completed"
                        }
                    elif queue_entry.status in ['FAILED', 'FAILED_MAX_RETRIES']:
                        result[catalog_id] = {
                            "status": "error",
                            "message": "Processing failed"
                        }
                else:
                    # No processing records found - check if catalog has products
                    product_count = len(catalog.products) if catalog.products else 0
                    if product_count > 0:
                        # Has products but no processing records - likely manually uploaded or legacy
                        result[catalog_id] = {
                            "status": "completed",
                            "message": f"Completed (legacy/manual upload) - {product_count} products found"
                        }
                    else:
                        # No products and no processing records - truly unprocessed
                        result[catalog_id] = {
                            "status": "unknown",
                            "message": "No processing history found - catalog may need parsing"
                        }

        return result

    except Exception as e:
        logger.error(f"Error fetching parsing statuses: {str(e)}\n{traceback.format_exc()}")
        # Return at least the in-memory statuses on error
        return PROCESSING_CATALOGS

@app.get("/parsing_status/{catalog_id}", response_model=Dict[str, Any])
async def get_parsing_status(catalog_id: int, admin_user: schemas.User = Depends(auth.require_admin)):
    # Check in-memory status first
    if catalog_id in PROCESSING_CATALOGS:
        return PROCESSING_CATALOGS[catalog_id]

    # Check database for historical status
    try:
        with SessionLocal() as db:
            catalog_exists = db.query(Catalog).filter(Catalog.id == catalog_id).first()
            if not catalog_exists:
                raise HTTPException(status_code=404, detail=f"Catalog ID {catalog_id} not found.")

            # Check ProcessedCatalogHashes for historical status
            hash_entry = db.query(ProcessedCatalogHashes).filter(
                ProcessedCatalogHashes.catalog_db_id == catalog_id
            ).order_by(ProcessedCatalogHashes.processed_at.desc()).first()

            # Check CatalogProcessingQueue for queue status
            queue_entry = db.query(CatalogProcessingQueue).filter(
                CatalogProcessingQueue.catalog_db_id == catalog_id
            ).order_by(CatalogProcessingQueue.added_at.desc()).first()

            # Determine status based on database records
            if hash_entry:
                if hash_entry.status == 'SUCCESS':
                    return {
                        "status": "completed",
                        "message": f"Processing completed successfully on {hash_entry.processed_at.strftime('%Y-%m-%d %H:%M')}"
                    }
                elif hash_entry.status == 'FAILED':
                    return {
                        "status": "error",
                        "message": f"Processing failed on {hash_entry.processed_at.strftime('%Y-%m-%d %H:%M')}"
                    }
                elif hash_entry.status == 'PENDING':
                    return {
                        "status": "queued",
                        "message": "Queued for processing"
                    }
            elif queue_entry:
                if queue_entry.status == 'PENDING':
                    return {
                        "status": "queued",
                        "message": "In processing queue"
                    }
                elif queue_entry.status == 'PROCESSING':
                    return {
                        "status": "processing",
                        "message": "Currently processing"
                    }
                elif queue_entry.status == 'SUCCESS':
                    return {
                        "status": "completed",
                        "message": "Processing completed"
                    }
                elif queue_entry.status in ['FAILED', 'FAILED_MAX_RETRIES']:
                    return {
                        "status": "error",
                        "message": "Processing failed"
                    }

            # No processing records found - check if catalog has products
            product_count = len(catalog_exists.products) if catalog_exists.products else 0
            if product_count > 0:
                # Has products but no processing records - likely manually uploaded or legacy
                return {
                    "status": "completed",
                    "message": f"Completed (legacy/manual upload) - {product_count} products found"
                }
            else:
                # No products and no processing records - truly unprocessed
                return {
                    "status": "unknown",
                    "message": "No processing history found - catalog may need parsing"
                }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching parsing status for catalog {catalog_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Error fetching parsing status")

@app.post("/api/admin/reconcile_status")
async def reconcile_status(admin_user: schemas.User = Depends(auth.require_admin)):
    """
    🔧 ENTERPRISE STATUS RECONCILIATION
    Manual trigger for status reconciliation. The system also runs this automatically.
    """
    try:
        result = auto_reconcile_status()
        return {
            "message": f"Manual reconciliation complete. Updated {result['reconciled_count']} catalogs.",
            "reconciliation_result": result
        }
    except Exception as e:
        logger.error(f"Error during manual reconciliation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error during reconciliation: {str(e)}")

@app.get("/api/admin/operation_logs")
async def get_operation_logs(
    catalog_id: Optional[int] = Query(None),
    limit: int = Query(50, le=200),
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """
    📊 ENTERPRISE OPERATION LOGS
    Get comprehensive operation history for monitoring and debugging.
    """
    try:
        logs = get_operation_history(catalog_id=catalog_id, limit=limit)
        return {
            "operation_logs": logs,
            "total_returned": len(logs)
        }
    except Exception as e:
        logger.error(f"Error fetching operation logs: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error fetching logs: {str(e)}")

@app.get("/api/admin/available_scrapers")
async def get_available_scrapers(
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """
    🔧 GET AVAILABLE SCRAPERS
    Returns list of available scraper configurations for manual scraping.
    """
    try:
        from catalog_processor import SCRAPER_CONFIG_DIR
        import json

        # Get all JSON config files from scrapers/configs/
        scraper_configs = []
        if SCRAPER_CONFIG_DIR.exists():
            for config_file in SCRAPER_CONFIG_DIR.glob("*.json"):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                        # Use the config filename without extension as the scraper name
                        scraper_name = config_file.stem
                        scraper_configs.append(scraper_name)
                except Exception as e:
                    logger.warning(f"Failed to read scraper config {config_file}: {e}")

        # Sort alphabetically for consistent UI
        scraper_configs.sort()

        logger.info(f"Available scrapers: {scraper_configs}")
        return scraper_configs

    except Exception as e:
        logger.error(f"Error fetching available scrapers: {e}")
        # Return fallback list if something goes wrong - matches actual config files
        return ["bilka", "brugsen", "discount365", "fotex", "lidl", "meny", "minkobmand", "netto", "rema1000", "spar", "superbrugsen"]

@app.post("/api/admin/manual_scrape/{store_name}")
async def manual_scrape(
    store_name: str,
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """
    🔧 MANUAL SCRAPE TRIGGER
    Manually trigger scraping for a specific store with full operation logging.
    """
    try:
        # Log the manual scrape request
        operation_log_id = log_operation(
            "SCRAPE", "STARTED",
            store_name=store_name,
            message=f"Manual scrape triggered for {store_name}",
            triggered_by="MANUAL"
        )

        # Import and run the scraper
        from catalog_processor import run_single_scraper

        # Run scraper in background to avoid blocking
        import asyncio
        import concurrent.futures

        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_single_scraper,
                store_name
            )

        # Complete the operation log
        if operation_log_id:
            if result and result.get("status") == "Queued":
                complete_operation(
                    operation_log_id, "SUCCESS",
                    message=f"Manual scrape completed for {store_name}",
                    metadata={"scrape_result": result}
                )
            else:
                complete_operation(
                    operation_log_id, "FAILED",
                    message=f"Manual scrape failed for {store_name}",
                    metadata={"scrape_result": result}
                )

        return {
            "message": f"Manual scrape completed for {store_name}",
            "result": result
        }

    except Exception as e:
        logger.error(f"Error during manual scrape for {store_name}: {e}", exc_info=True)

        # Log the error
        if 'operation_log_id' in locals() and operation_log_id:
            complete_operation(
                operation_log_id, "FAILED",
                message=f"Manual scrape error: {str(e)}",
                metadata={"error": str(e)}
            )

        raise HTTPException(status_code=500, detail=f"Error during manual scrape: {str(e)}")

@app.post("/api/admin/cleanup_memory")
async def cleanup_memory_status(
    max_age_hours: int = Query(24, ge=1, le=168),  # 1 hour to 1 week
    admin_user: schemas.User = Depends(auth.require_admin)
):
    """
    🧹 MANUAL MEMORY CLEANUP
    Manually trigger cleanup of stale in-memory status entries.
    """
    try:
        cleaned_count = cleanup_stale_in_memory_status(PROCESSING_CATALOGS, max_age_hours=max_age_hours)

        return {
            "message": f"Memory cleanup complete. Removed {cleaned_count} stale entries.",
            "cleaned_count": cleaned_count,
            "max_age_hours": max_age_hours,
            "remaining_entries": len(PROCESSING_CATALOGS)
        }

    except Exception as e:
        logger.error(f"Error during manual memory cleanup: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error during cleanup: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    import os
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)