"""
Admin Data Service

Comprehensive data retrieval service for admin panel functionality.
Provides user search, analytics, and deep-dive capabilities with
optimized queries and proper pagination.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc, and_, or_, text, case
from sqlalchemy.exc import SQLAlchemyError

from database import (
    User, UserQuery, UserSession, UserInteraction, 
    UserPreferencesHistory, AdminAuditLog, get_db
)
from user_activity_constants import QUERY_CATEGORIES, INTERACTION_TYPES

logger = logging.getLogger(__name__)

class AdminDataService:
    """
    Service class for admin data retrieval and analytics.
    Provides optimized queries for user search, analytics, and deep-dive functionality.
    """
    
    def __init__(self, db_session: Optional[Session] = None):
        self.db_session = db_session
        self._should_close_session = db_session is None
    
    def _get_db_session(self) -> Session:
        """Get database session, creating one if needed."""
        if self.db_session:
            return self.db_session
        return next(get_db())
    
    def _close_db_session(self, db: Session):
        """Close database session if we created it."""
        if self._should_close_session:
            db.close()
    
    # --- User Search and Retrieval ---
    
    def search_users(
        self,
        search_term: Optional[str] = None,
        user_type: Optional[str] = None,  # "active", "inactive", "power_user"
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 50,
        offset: int = 0
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        Search users with filters and pagination.
        
        Returns:
            Tuple of (users_list, total_count)
        """
        db = self._get_db_session()
        
        try:
            # Base query with user stats
            query = db.query(
                User.id,
                User.username,
                User.is_admin,
                User.created_at,
                func.count(UserQuery.id).label('total_queries'),
                func.count(UserSession.id).label('total_sessions'),
                func.max(UserSession.session_start).label('last_activity'),
                func.avg(UserSession.duration_seconds).label('avg_session_duration')
            ).outerjoin(UserQuery).outerjoin(UserSession).group_by(User.id)
            
            # Apply filters
            if search_term:
                query = query.filter(User.username.ilike(f'%{search_term}%'))
            
            if date_from:
                query = query.having(func.max(UserSession.session_start) >= date_from)
            
            if date_to:
                query = query.having(func.max(UserSession.session_start) <= date_to)
            
            # Get total count before pagination
            total_count = query.count()
            
            # Apply user type filter (after getting count)
            if user_type == "active":
                # Users with activity in last 30 days
                thirty_days_ago = datetime.utcnow() - timedelta(days=30)
                query = query.having(func.max(UserSession.session_start) >= thirty_days_ago)
            elif user_type == "inactive":
                # Users with no activity in last 30 days
                thirty_days_ago = datetime.utcnow() - timedelta(days=30)
                query = query.having(
                    or_(
                        func.max(UserSession.session_start) < thirty_days_ago,
                        func.max(UserSession.session_start).is_(None)
                    )
                )
            elif user_type == "power_user":
                # Users with 10+ queries or 5+ sessions
                query = query.having(
                    or_(
                        func.count(UserQuery.id) >= 10,
                        func.count(UserSession.id) >= 5
                    )
                )
            
            # Apply pagination and ordering
            users = query.order_by(desc('last_activity')).offset(offset).limit(limit).all()
            
            # Format results
            users_list = []
            for user in users:
                users_list.append({
                    'id': user.id,
                    'username': user.username,
                    'is_admin': user.is_admin,
                    'created_at': user.created_at.isoformat() if user.created_at else None,
                    'total_queries': user.total_queries or 0,
                    'total_sessions': user.total_sessions or 0,
                    'last_activity': user.last_activity.isoformat() if user.last_activity else None,
                    'avg_session_duration': int(user.avg_session_duration) if user.avg_session_duration else 0,
                    'user_type': self._classify_user_type(user)
                })
            
            return users_list, total_count
            
        except SQLAlchemyError as e:
            logger.error(f"Database error in search_users: {str(e)}")
            return [], 0
        finally:
            self._close_db_session(db)
    
    def get_user_profile(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get comprehensive user profile with activity summary."""
        db = self._get_db_session()
        
        try:
            # Get user with basic stats
            user_stats = db.query(
                User.id,
                User.username,
                User.is_admin,
                User.created_at,
                func.count(UserQuery.id).label('total_queries'),
                func.count(UserSession.id).label('total_sessions'),
                func.count(UserInteraction.id).label('total_interactions'),
                func.min(UserSession.session_start).label('first_activity'),
                func.max(UserSession.session_start).label('last_activity'),
                func.avg(UserSession.duration_seconds).label('avg_session_duration'),
                func.sum(UserSession.queries_count).label('total_session_queries')
            ).outerjoin(UserQuery).outerjoin(UserSession).outerjoin(UserInteraction)\
             .filter(User.id == user_id).group_by(User.id).first()
            
            if not user_stats:
                return None
            
            # Get query categories breakdown
            query_categories = db.query(
                UserQuery.query_category,
                func.count(UserQuery.id).label('count')
            ).filter(UserQuery.user_id == user_id)\
             .filter(UserQuery.query_category.isnot(None))\
             .group_by(UserQuery.query_category).all()
            
            # Get interaction types breakdown
            interaction_types = db.query(
                UserInteraction.interaction_type,
                func.count(UserInteraction.id).label('count')
            ).filter(UserInteraction.user_id == user_id)\
             .group_by(UserInteraction.interaction_type).all()
            
            # Get recent activity (last 10 actions)
            recent_queries = db.query(UserQuery).filter(UserQuery.user_id == user_id)\
                           .order_by(desc(UserQuery.created_at)).limit(5).all()
            
            recent_interactions = db.query(UserInteraction).filter(UserInteraction.user_id == user_id)\
                                .order_by(desc(UserInteraction.created_at)).limit(5).all()
            
            # Format profile data
            profile = {
                'id': user_stats.id,
                'username': user_stats.username,
                'is_admin': user_stats.is_admin,
                'created_at': user_stats.created_at.isoformat() if user_stats.created_at else None,
                'first_activity': user_stats.first_activity.isoformat() if user_stats.first_activity else None,
                'last_activity': user_stats.last_activity.isoformat() if user_stats.last_activity else None,
                'stats': {
                    'total_queries': user_stats.total_queries or 0,
                    'total_sessions': user_stats.total_sessions or 0,
                    'total_interactions': user_stats.total_interactions or 0,
                    'avg_session_duration': int(user_stats.avg_session_duration) if user_stats.avg_session_duration else 0,
                    'total_session_queries': user_stats.total_session_queries or 0
                },
                'query_categories': {cat.query_category: cat.count for cat in query_categories},
                'interaction_types': {int_type.interaction_type: int_type.count for int_type in interaction_types},
                'recent_queries': [
                    {
                        'id': q.id,
                        'query_text': q.query_text,
                        'created_at': q.created_at.isoformat(),
                        'was_successful': q.was_successful,
                        'results_count': q.results_count
                    } for q in recent_queries
                ],
                'recent_interactions': [
                    {
                        'id': i.id,
                        'interaction_type': i.interaction_type,
                        'target_type': i.target_type,
                        'target_id': i.target_id,
                        'created_at': i.created_at.isoformat()
                    } for i in recent_interactions
                ],
                'user_type': self._classify_user_type(user_stats)
            }
            
            return profile
            
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_user_profile: {str(e)}")
            return None
        finally:
            self._close_db_session(db)
    
    # --- User Activity Timeline ---
    
    def get_user_activity_timeline(
        self,
        user_id: int,
        activity_type: Optional[str] = None,  # "queries", "interactions", "sessions"
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get chronological timeline of user activities."""
        db = self._get_db_session()
        
        try:
            timeline = []
            
            # Get queries if requested
            if not activity_type or activity_type == "queries":
                query_filter = UserQuery.user_id == user_id
                if date_from:
                    query_filter = and_(query_filter, UserQuery.created_at >= date_from)
                if date_to:
                    query_filter = and_(query_filter, UserQuery.created_at <= date_to)
                
                queries = db.query(UserQuery).filter(query_filter)\
                           .order_by(desc(UserQuery.created_at)).limit(limit).all()
                
                for query in queries:
                    timeline.append({
                        'type': 'query',
                        'id': query.id,
                        'timestamp': query.created_at.isoformat(),
                        'data': {
                            'query_text': query.query_text,
                            'selected_stores': query.selected_stores,
                            'results_count': query.results_count,
                            'was_successful': query.was_successful,
                            'response_time_ms': query.response_time_ms,
                            'query_category': query.query_category
                        }
                    })
            
            # Get interactions if requested
            if not activity_type or activity_type == "interactions":
                interaction_filter = UserInteraction.user_id == user_id
                if date_from:
                    interaction_filter = and_(interaction_filter, UserInteraction.created_at >= date_from)
                if date_to:
                    interaction_filter = and_(interaction_filter, UserInteraction.created_at <= date_to)
                
                interactions = db.query(UserInteraction).filter(interaction_filter)\
                             .order_by(desc(UserInteraction.created_at)).limit(limit).all()
                
                for interaction in interactions:
                    timeline.append({
                        'type': 'interaction',
                        'id': interaction.id,
                        'timestamp': interaction.created_at.isoformat(),
                        'data': {
                            'interaction_type': interaction.interaction_type,
                            'target_type': interaction.target_type,
                            'target_id': interaction.target_id,
                            'source_query_id': interaction.source_query_id,
                            'interaction_data': interaction.interaction_data
                        }
                    })
            
            # Get sessions if requested
            if not activity_type or activity_type == "sessions":
                session_filter = UserSession.user_id == user_id
                if date_from:
                    session_filter = and_(session_filter, UserSession.session_start >= date_from)
                if date_to:
                    session_filter = and_(session_filter, UserSession.session_start <= date_to)
                
                sessions = db.query(UserSession).filter(session_filter)\
                          .order_by(desc(UserSession.session_start)).limit(limit).all()
                
                for session in sessions:
                    timeline.append({
                        'type': 'session',
                        'id': session.id,
                        'timestamp': session.session_start.isoformat(),
                        'data': {
                            'session_id': session.session_id,
                            'duration_seconds': session.duration_seconds,
                            'queries_count': session.queries_count,
                            'interactions_count': session.interactions_count,
                            'device_type': session.device_type,
                            'browser': session.browser
                        }
                    })
            
            # Sort timeline by timestamp (most recent first)
            timeline.sort(key=lambda x: x['timestamp'], reverse=True)
            
            # Apply pagination
            return timeline[offset:offset + limit]
            
        except SQLAlchemyError as e:
            logger.error(f"Database error in get_user_activity_timeline: {str(e)}")
            return []
        finally:
            self._close_db_session(db)
    
    def _classify_user_type(self, user_stats) -> str:
        """Classify user type based on activity stats."""
        total_queries = getattr(user_stats, 'total_queries', 0) or 0
        total_sessions = getattr(user_stats, 'total_sessions', 0) or 0
        last_activity = getattr(user_stats, 'last_activity', None)
        
        # Check if user is active (activity in last 30 days)
        is_active = False
        if last_activity:
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            is_active = last_activity >= thirty_days_ago
        
        # Classify user type
        if total_queries >= 20 or total_sessions >= 10:
            return "power_user"
        elif total_queries >= 5 or total_sessions >= 3:
            return "regular_user" if is_active else "inactive_regular"
        elif total_queries > 0 or total_sessions > 0:
            return "casual_user" if is_active else "inactive_casual"
        else:
            return "new_user"


    # --- Analytics and Business Intelligence ---

    def get_system_analytics(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get system-wide analytics and metrics."""
        db = self._get_db_session()

        try:
            # Set default date range (last 30 days)
            if not date_from:
                date_from = datetime.utcnow() - timedelta(days=30)
            if not date_to:
                date_to = datetime.utcnow()

            # User metrics
            total_users = db.query(func.count(User.id)).scalar()
            active_users = db.query(func.count(User.id))\
                          .join(UserSession)\
                          .filter(UserSession.session_start >= date_from).scalar()

            # Query metrics
            total_queries = db.query(func.count(UserQuery.id))\
                           .filter(UserQuery.created_at >= date_from).scalar()
            successful_queries = db.query(func.count(UserQuery.id))\
                               .filter(UserQuery.created_at >= date_from)\
                               .filter(UserQuery.was_successful == True).scalar()
            avg_response_time = db.query(func.avg(UserQuery.response_time_ms))\
                              .filter(UserQuery.created_at >= date_from)\
                              .filter(UserQuery.was_successful == True).scalar()

            # Session metrics
            total_sessions = db.query(func.count(UserSession.id))\
                           .filter(UserSession.session_start >= date_from).scalar()
            avg_session_duration = db.query(func.avg(UserSession.duration_seconds))\
                                 .filter(UserSession.session_start >= date_from).scalar()

            # Interaction metrics
            total_interactions = db.query(func.count(UserInteraction.id))\
                               .filter(UserInteraction.created_at >= date_from).scalar()

            # Popular queries
            popular_queries = db.query(
                UserQuery.query_text,
                func.count(UserQuery.id).label('count')
            ).filter(UserQuery.created_at >= date_from)\
             .group_by(UserQuery.query_text)\
             .order_by(desc('count')).limit(10).all()

            # Query categories breakdown
            category_breakdown = db.query(
                UserQuery.query_category,
                func.count(UserQuery.id).label('count')
            ).filter(UserQuery.created_at >= date_from)\
             .filter(UserQuery.query_category.isnot(None))\
             .group_by(UserQuery.query_category)\
             .order_by(desc('count')).all()

            return {
                'date_range': {
                    'from': date_from.isoformat(),
                    'to': date_to.isoformat()
                },
                'users': {
                    'total': total_users,
                    'active': active_users,
                    'activity_rate': (active_users / total_users * 100) if total_users > 0 else 0
                },
                'queries': {
                    'total': total_queries,
                    'successful': successful_queries,
                    'success_rate': (successful_queries / total_queries * 100) if total_queries > 0 else 0,
                    'avg_response_time_ms': int(avg_response_time) if avg_response_time else 0
                },
                'sessions': {
                    'total': total_sessions,
                    'avg_duration_seconds': int(avg_session_duration) if avg_session_duration else 0
                },
                'interactions': {
                    'total': total_interactions
                },
                'popular_queries': [
                    {'query': q.query_text, 'count': q.count} for q in popular_queries
                ],
                'category_breakdown': [
                    {'category': c.query_category, 'count': c.count} for c in category_breakdown
                ]
            }

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_system_analytics: {str(e)}")
            return {}
        finally:
            self._close_db_session(db)

    def get_query_analytics(
        self,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        limit: int = 50
    ) -> Dict[str, Any]:
        """Get detailed query analytics and patterns."""
        db = self._get_db_session()

        try:
            if not date_from:
                date_from = datetime.utcnow() - timedelta(days=30)
            if not date_to:
                date_to = datetime.utcnow()

            # Most popular queries
            popular_queries = db.query(
                UserQuery.query_text,
                func.count(UserQuery.id).label('frequency'),
                func.avg(UserQuery.response_time_ms).label('avg_response_time'),
                func.avg(UserQuery.results_count).label('avg_results'),
                func.sum(case([(UserQuery.was_successful == True, 1)], else_=0)).label('successful_count')
            ).filter(UserQuery.created_at >= date_from)\
             .group_by(UserQuery.query_text)\
             .order_by(desc('frequency')).limit(limit).all()

            # Failed queries analysis
            failed_queries = db.query(
                UserQuery.query_text,
                UserQuery.error_message,
                func.count(UserQuery.id).label('failure_count')
            ).filter(UserQuery.created_at >= date_from)\
             .filter(UserQuery.was_successful == False)\
             .group_by(UserQuery.query_text, UserQuery.error_message)\
             .order_by(desc('failure_count')).limit(20).all()

            # Query refinement patterns
            refinements = db.query(
                UserInteraction.interaction_data
            ).filter(UserInteraction.created_at >= date_from)\
             .filter(UserInteraction.interaction_type == 'query_refined').all()

            return {
                'popular_queries': [
                    {
                        'query': q.query_text,
                        'frequency': q.frequency,
                        'avg_response_time_ms': int(q.avg_response_time) if q.avg_response_time else 0,
                        'avg_results': int(q.avg_results) if q.avg_results else 0,
                        'success_rate': (q.successful_count / q.frequency * 100) if q.frequency > 0 else 0
                    } for q in popular_queries
                ],
                'failed_queries': [
                    {
                        'query': f.query_text,
                        'error_message': f.error_message,
                        'failure_count': f.failure_count
                    } for f in failed_queries
                ],
                'refinement_count': len(refinements)
            }

        except SQLAlchemyError as e:
            logger.error(f"Database error in get_query_analytics: {str(e)}")
            return {}
        finally:
            self._close_db_session(db)


# --- Global Admin Data Service Instance ---
admin_data_service = AdminDataService()
