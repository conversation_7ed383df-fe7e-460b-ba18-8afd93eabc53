#!/usr/bin/env python3
"""
Test script for the user activity tracking system.
This will verify all components are working correctly.
"""

import os
import sys
import traceback
from datetime import datetime

# Set environment variables directly to avoid .env parsing issues
os.environ['DATABASE_URL'] = 'postgresql://postgres01_3uoc_user:<EMAIL>/postgres01_3uoc'

def test_database_connection():
    """Test basic database connection"""
    print("🔍 Testing database connection...")
    try:
        from database import engine
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_user_activity_tables():
    """Test if user activity tables exist and are accessible"""
    print("\n🔍 Testing user activity tables...")
    try:
        from database import engine
        from sqlalchemy import text
        with engine.connect() as conn:
            # Check which user activity tables exist
            result = conn.execute(text("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public' AND table_name LIKE 'user_%'
                ORDER BY table_name
            """))
            tables = [row[0] for row in result.fetchall()]
            
            print("Found user activity tables:")
            for table in tables:
                print(f"  • {table}")
            
            # Test access to each table
            expected_tables = ['user_queries', 'user_sessions', 'user_interactions', 'user_preferences_history']
            for table in expected_tables:
                if table in tables:
                    try:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.fetchone()[0]
                        print(f"✅ {table}: {count} records")
                    except Exception as e:
                        print(f"❌ {table}: Error accessing - {e}")
                else:
                    print(f"❌ {table}: Table missing")

            # Check admin_audit_log
            result = conn.execute(text("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public' AND table_name = 'admin_audit_log'
            """))
            if result.fetchone():
                result = conn.execute(text("SELECT COUNT(*) FROM admin_audit_log"))
                count = result.fetchone()[0]
                print(f"✅ admin_audit_log: {count} records")
            else:
                print("❌ admin_audit_log: Table missing")
            
            return True
    except Exception as e:
        print(f"❌ Error testing tables: {e}")
        traceback.print_exc()
        return False

def test_user_activity_logger():
    """Test the user activity logger"""
    print("\n🔍 Testing user activity logger...")
    try:
        from user_activity_logger import activity_logger
        from database import get_db
        
        # Test logging a query
        query_id = activity_logger.log_query(
            user_id=None,  # Anonymous user
            session_id="test_session_123",
            query_text="test pizza query",
            selected_stores=[1, 2, 3],
            query_model="test_model",
            results_count=5,
            response_time_ms=250,
            was_successful=True
        )
        
        if query_id:
            print(f"✅ Query logged successfully with ID: {query_id}")
        else:
            print("❌ Query logging failed")

        return query_id is not None
        
    except Exception as e:
        print(f"❌ Error testing activity logger: {e}")
        traceback.print_exc()
        return False

def test_session_middleware():
    """Test session tracking middleware"""
    print("\n🔍 Testing session middleware...")
    try:
        import uuid

        # Test session ID generation (same logic as middleware)
        session_id = f"sess_{uuid.uuid4().hex}"

        if session_id and len(session_id) > 10:
            print(f"✅ Session ID generated: {session_id[:20]}...")

            # Test middleware import
            from session_tracking_middleware import SessionTrackingMiddleware
            middleware = SessionTrackingMiddleware(None)
            print("✅ Session middleware imported successfully")
            return True
        else:
            print("❌ Session ID generation failed")
            return False

    except Exception as e:
        print(f"❌ Error testing session middleware: {e}")
        traceback.print_exc()
        return False

def test_admin_data_service():
    """Test admin data service"""
    print("\n🔍 Testing admin data service...")
    try:
        from admin_data_service import AdminDataService

        # Create service instance
        service = AdminDataService()

        # Test user search
        users, total_count = service.search_users(limit=5)
        print(f"✅ User search returned {len(users)} users (total: {total_count})")

        # Test system analytics
        analytics = service.get_system_analytics()
        if analytics and 'users' in analytics:
            print(f"✅ System analytics returned data: {analytics['users']['total']} total users")
            return True
        else:
            print("❌ System analytics failed or no data")
            print(f"Analytics keys: {list(analytics.keys()) if analytics else 'None'}")
            return False

    except Exception as e:
        print(f"❌ Error testing admin data service: {e}")
        traceback.print_exc()
        return False

def test_advanced_interaction_logger():
    """Test advanced interaction logger"""
    print("\n🔍 Testing advanced interaction logger...")
    try:
        from advanced_interaction_logger import advanced_interaction_logger
        
        # Test product click logging
        interaction_id = advanced_interaction_logger.log_product_click(
            product_id=999,
            product_name="Test Pizza Product",
            product_price=29.95,
            store_id=1,
            store_name="Test Store",
            position_in_results=1,
            user_id=None,
            session_id="test_session_123"
        )
        
        if interaction_id:
            print(f"✅ Product click logged with ID: {interaction_id}")
            return True
        else:
            print("❌ Product click logging failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing interaction logger: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Tilbudsjægeren User Activity System")
    print("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("User Activity Tables", test_user_activity_tables),
        ("User Activity Logger", test_user_activity_logger),
        ("Session Middleware", test_session_middleware),
        ("Admin Data Service", test_admin_data_service),
        ("Advanced Interaction Logger", test_advanced_interaction_logger),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED! User activity system is ready!")
        print("\nNext steps:")
        print("1. Start your FastAPI server: python start_backend.py")
        print("2. Test the admin API endpoints")
        print("3. Set up the admin interface")
    else:
        print(f"\n⚠️  {len(results) - passed} tests failed. Check the errors above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
