<!DOCTYPE html>
<html lang="da">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brugerprofil | Admin | Tilbudsjægeren</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .user-type-badge {
            @apply px-3 py-1 rounded-full text-sm font-medium;
        }
        .power-user { @apply bg-purple-100 text-purple-800; }
        .regular-user { @apply bg-green-100 text-green-800; }
        .casual-user { @apply bg-blue-100 text-blue-800; }
        .inactive-regular { @apply bg-yellow-100 text-yellow-800; }
        .inactive-casual { @apply bg-gray-100 text-gray-800; }
        .new-user { @apply bg-indigo-100 text-indigo-800; }
        
        .stat-card {
            @apply bg-white rounded-lg shadow p-6 border-l-4;
        }
        .stat-card.primary { @apply border-blue-500; }
        .stat-card.success { @apply border-green-500; }
        .stat-card.warning { @apply border-yellow-500; }
        .stat-card.info { @apply border-purple-500; }
        
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-4">
                    <button onclick="goBack()" class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <h1 class="text-xl font-semibold text-gray-900">
                        <i class="fas fa-user mr-2 text-blue-600"></i>
                        Brugerprofil
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">Logget ind som admin</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i> Log ud
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Loading State -->
        <div id="loadingState" class="text-center py-12">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-500">Indlæser brugerprofil...</p>
        </div>

        <!-- Profile Content -->
        <div id="profileContent" class="hidden">
            <!-- User Header -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center">
                                <i class="fas fa-user text-3xl text-gray-600"></i>
                            </div>
                            <div>
                                <h2 id="userName" class="text-2xl font-bold text-gray-900">-</h2>
                                <p class="text-gray-500">Bruger ID: <span id="userId">-</span></p>
                                <div class="mt-2 flex items-center space-x-3">
                                    <span id="userTypeBadge" class="user-type-badge">-</span>
                                    <span id="adminBadge" class="hidden px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Admin</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <button onclick="viewTimeline()" 
                                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <i class="fas fa-history mr-2"></i>
                                Se Tidslinje
                            </button>
                            <button onclick="viewQueries()" 
                                    class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                <i class="fas fa-search mr-2"></i>
                                Se Søgninger
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="stat-card primary">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-search text-2xl text-blue-500"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Søgninger</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalQueries">-</p>
                        </div>
                    </div>
                </div>

                <div class="stat-card success">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-2xl text-green-500"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Sessioner</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalSessions">-</p>
                        </div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-mouse-pointer text-2xl text-purple-500"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Interaktioner</p>
                            <p class="text-2xl font-semibold text-gray-900" id="totalInteractions">-</p>
                        </div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-stopwatch text-2xl text-yellow-500"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Ø Session</p>
                            <p class="text-2xl font-semibold text-gray-900" id="avgSessionDuration">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Analysis -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Query Categories -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-chart-pie mr-2 text-blue-600"></i>
                            Søgekategorier
                        </h3>
                    </div>
                    <div class="p-6">
                        <canvas id="categoriesChart" width="400" height="300"></canvas>
                    </div>
                </div>

                <!-- Interaction Types -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-chart-bar mr-2 text-green-600"></i>
                            Interaktionstyper
                        </h3>
                    </div>
                    <div class="p-6">
                        <canvas id="interactionsChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Queries -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-search mr-2 text-purple-600"></i>
                            Seneste Søgninger
                        </h3>
                    </div>
                    <div class="p-6">
                        <div id="recentQueries" class="space-y-4">
                            <!-- Dynamic content -->
                        </div>
                    </div>
                </div>

                <!-- Recent Interactions -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-mouse-pointer mr-2 text-yellow-600"></i>
                            Seneste Interaktioner
                        </h3>
                    </div>
                    <div class="p-6">
                        <div id="recentInteractions" class="space-y-4">
                            <!-- Dynamic content -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Timeline Summary -->
            <div class="mt-6 bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-calendar-alt mr-2 text-red-600"></i>
                            Aktivitetsoversigt
                        </h3>
                        <button onclick="viewTimeline()" 
                                class="text-blue-600 hover:text-blue-800 text-sm">
                            Se fuld tidslinje <i class="fas fa-arrow-right ml-1"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900" id="firstActivity">-</div>
                            <div class="text-sm text-gray-500">Første aktivitet</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900" id="lastActivity">-</div>
                            <div class="text-sm text-gray-500">Sidste aktivitet</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-gray-900" id="daysSinceJoined">-</div>
                            <div class="text-sm text-gray-500">Dage som bruger</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * Admin User Profile JavaScript
         * 
         * Handles user profile data loading and display.
         */
        
        class AdminUserProfile {
            constructor() {
                this.userId = this.getUserIdFromUrl();
                this.authToken = this.getAuthToken();
                this.charts = {};
                this.init();
            }
            
            init() {
                if (!this.userId) {
                    alert('Bruger ID ikke fundet i URL');
                    this.goBack();
                    return;
                }
                
                this.loadUserProfile();
            }
            
            getUserIdFromUrl() {
                const pathParts = window.location.pathname.split('/');
                const userIndex = pathParts.indexOf('users');
                return userIndex !== -1 && pathParts[userIndex + 1] ? 
                       parseInt(pathParts[userIndex + 1]) : null;
            }
            
            getAuthToken() {
                return localStorage.getItem('admin_token') || 
                       this.getCookie('admin_token') || 
                       'your_admin_token_here';
            }
            
            getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
            }
            
            async apiCall(endpoint, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`,
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                };
                
                try {
                    const response = await fetch(endpoint, { ...defaultOptions, ...options });
                    
                    if (!response.ok) {
                        if (response.status === 401) {
                            this.handleAuthError();
                            return null;
                        }
                        if (response.status === 404) {
                            alert('Bruger ikke fundet');
                            this.goBack();
                            return null;
                        }
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    return await response.json();
                } catch (error) {
                    console.error('API call failed:', error);
                    return null;
                }
            }
            
            handleAuthError() {
                alert('Din session er udløbet. Du bliver omdirigeret til login.');
                window.location.href = '/admin/login';
            }
            
            async loadUserProfile() {
                this.showLoading(true);
                
                try {
                    const profile = await this.apiCall(`/api/admin/users/${this.userId}/profile`);
                    
                    if (profile) {
                        this.displayProfile(profile);
                        this.createCharts(profile);
                    }
                } catch (error) {
                    console.error('Failed to load user profile:', error);
                } finally {
                    this.showLoading(false);
                }
            }
            
            showLoading(show) {
                const loadingState = document.getElementById('loadingState');
                const profileContent = document.getElementById('profileContent');
                
                if (show) {
                    loadingState.classList.remove('hidden');
                    profileContent.classList.add('hidden');
                } else {
                    loadingState.classList.add('hidden');
                    profileContent.classList.remove('hidden');
                }
            }
            
            displayProfile(profile) {
                // Update header
                document.getElementById('userName').textContent = profile.username;
                document.getElementById('userId').textContent = profile.id;
                
                const userTypeBadge = document.getElementById('userTypeBadge');
                userTypeBadge.textContent = this.getUserTypeLabel(profile.user_type);
                userTypeBadge.className = `user-type-badge ${profile.user_type}`;
                
                if (profile.is_admin) {
                    document.getElementById('adminBadge').classList.remove('hidden');
                }
                
                // Update stats
                document.getElementById('totalQueries').textContent = profile.stats.total_queries.toLocaleString('da-DK');
                document.getElementById('totalSessions').textContent = profile.stats.total_sessions.toLocaleString('da-DK');
                document.getElementById('totalInteractions').textContent = profile.stats.total_interactions.toLocaleString('da-DK');
                document.getElementById('avgSessionDuration').textContent = this.formatDuration(profile.stats.avg_session_duration);
                
                // Update activity dates
                document.getElementById('firstActivity').textContent = profile.first_activity ? 
                    new Date(profile.first_activity).toLocaleDateString('da-DK') : 'Ingen';
                document.getElementById('lastActivity').textContent = profile.last_activity ? 
                    new Date(profile.last_activity).toLocaleDateString('da-DK') : 'Ingen';
                
                if (profile.created_at) {
                    const daysSince = Math.floor((new Date() - new Date(profile.created_at)) / (1000 * 60 * 60 * 24));
                    document.getElementById('daysSinceJoined').textContent = daysSince;
                }
                
                // Display recent activities
                this.displayRecentQueries(profile.recent_queries);
                this.displayRecentInteractions(profile.recent_interactions);
            }
            
            createCharts(profile) {
                this.createCategoriesChart(profile.query_categories);
                this.createInteractionsChart(profile.interaction_types);
            }
            
            createCategoriesChart(categories) {
                const ctx = document.getElementById('categoriesChart').getContext('2d');
                
                if (this.charts.categories) {
                    this.charts.categories.destroy();
                }
                
                const colors = [
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(139, 92, 246, 0.8)'
                ];
                
                const data = Object.entries(categories || {});
                
                this.charts.categories = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.map(([category]) => this.getCategoryLabel(category)),
                        datasets: [{
                            data: data.map(([, count]) => count),
                            backgroundColor: colors.slice(0, data.length),
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
            
            createInteractionsChart(interactions) {
                const ctx = document.getElementById('interactionsChart').getContext('2d');
                
                if (this.charts.interactions) {
                    this.charts.interactions.destroy();
                }
                
                const data = Object.entries(interactions || {});
                
                this.charts.interactions = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.map(([type]) => this.getInteractionLabel(type)),
                        datasets: [{
                            label: 'Antal',
                            data: data.map(([, count]) => count),
                            backgroundColor: 'rgba(16, 185, 129, 0.8)',
                            borderColor: 'rgba(16, 185, 129, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
            
            displayRecentQueries(queries) {
                const container = document.getElementById('recentQueries');
                
                if (!queries || queries.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-4">Ingen seneste søgninger</p>';
                    return;
                }
                
                container.innerHTML = queries.map(query => `
                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                        <div class="font-medium text-gray-900">${query.query_text}</div>
                        <div class="text-sm text-gray-500">
                            ${new Date(query.created_at).toLocaleDateString('da-DK')} • 
                            ${query.results_count} resultater • 
                            ${query.was_successful ? 'Succesfuld' : 'Fejlet'}
                        </div>
                    </div>
                `).join('');
            }
            
            displayRecentInteractions(interactions) {
                const container = document.getElementById('recentInteractions');
                
                if (!interactions || interactions.length === 0) {
                    container.innerHTML = '<p class="text-gray-500 text-center py-4">Ingen seneste interaktioner</p>';
                    return;
                }
                
                container.innerHTML = interactions.map(interaction => `
                    <div class="border-l-4 border-green-500 pl-4 py-2">
                        <div class="font-medium text-gray-900">${this.getInteractionLabel(interaction.interaction_type)}</div>
                        <div class="text-sm text-gray-500">
                            ${new Date(interaction.created_at).toLocaleDateString('da-DK')} • 
                            ${interaction.target_type}: ${interaction.target_id}
                        </div>
                    </div>
                `).join('');
            }
            
            getUserTypeLabel(userType) {
                const labels = {
                    'power_user': 'Power Bruger',
                    'regular_user': 'Almindelig',
                    'casual_user': 'Lejlighedsbruger',
                    'inactive_regular': 'Inaktiv',
                    'inactive_casual': 'Inaktiv',
                    'new_user': 'Ny Bruger'
                };
                return labels[userType] || userType;
            }
            
            getCategoryLabel(category) {
                const labels = {
                    'food_beverages': 'Mad & Drikke',
                    'household_cleaning': 'Husholdning',
                    'electronics': 'Elektronik',
                    'personal_care': 'Personlig pleje',
                    'home_garden': 'Hjem & Have'
                };
                return labels[category] || category;
            }
            
            getInteractionLabel(type) {
                const labels = {
                    'product_clicked': 'Produktklik',
                    'catalog_viewed': 'Katalogvisning',
                    'store_selected': 'Butikvalg',
                    'query_refined': 'Søgeforbedring'
                };
                return labels[type] || type;
            }
            
            formatDuration(seconds) {
                if (!seconds) return '0 min';
                if (seconds < 60) return `${seconds}s`;
                const minutes = Math.floor(seconds / 60);
                if (minutes < 60) return `${minutes} min`;
                const hours = Math.floor(minutes / 60);
                return `${hours}t ${minutes % 60}m`;
            }
            
            goBack() {
                window.history.back();
            }
            
            viewTimeline() {
                window.location.href = `/admin/users/${this.userId}/timeline`;
            }
            
            viewQueries() {
                window.location.href = `/admin/users/${this.userId}/queries`;
            }
        }
        
        // Global functions
        function goBack() {
            adminUserProfile.goBack();
        }
        
        function viewTimeline() {
            adminUserProfile.viewTimeline();
        }
        
        function viewQueries() {
            adminUserProfile.viewQueries();
        }
        
        function logout() {
            localStorage.removeItem('admin_token');
            window.location.href = '/admin/login';
        }
        
        // Initialize profile
        let adminUserProfile;
        document.addEventListener('DOMContentLoaded', () => {
            adminUserProfile = new AdminUserProfile();
        });
    </script>
</body>
</html>
