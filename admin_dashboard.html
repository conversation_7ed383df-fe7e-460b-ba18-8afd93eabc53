<!DOCTYPE html>
<html lang="da">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | Tilbudsjægeren</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            @apply bg-white rounded-lg shadow p-6 border-l-4;
        }
        .metric-card.users { @apply border-blue-500; }
        .metric-card.queries { @apply border-green-500; }
        .metric-card.sessions { @apply border-purple-500; }
        .metric-card.interactions { @apply border-yellow-500; }
        
        .nav-item {
            @apply flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900 rounded-md transition-colors;
        }
        .nav-item.active {
            @apply bg-blue-100 text-blue-700;
        }
        
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">
                        <i class="fas fa-tachometer-alt mr-2 text-blue-600"></i>
                        Tilbudsjægeren Admin Dashboard
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-500">Logget ind som admin</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i> Log ud
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-sm h-screen">
            <div class="p-4">
                <nav class="space-y-2">
                    <a href="/admin/dashboard" class="nav-item active">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>
                    <a href="/admin/users" class="nav-item">
                        <i class="fas fa-users mr-3"></i>
                        Brugersøgning
                    </a>
                    <a href="/admin/analytics" class="nav-item">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Analytics
                    </a>
                    <a href="/admin/queries" class="nav-item">
                        <i class="fas fa-search mr-3"></i>
                        Søgeanalyse
                    </a>
                    <a href="/admin/sessions" class="nav-item">
                        <i class="fas fa-clock mr-3"></i>
                        Sessioner
                    </a>
                    <a href="/admin/exports" class="nav-item">
                        <i class="fas fa-download mr-3"></i>
                        Data Export
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-6">
            <!-- Header -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900">Dashboard Oversigt</h2>
                <p class="mt-1 text-sm text-gray-600">
                    Systemstatus og nøgletal for de sidste 30 dage
                </p>
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-12">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-500">Indlæser dashboard data...</p>
            </div>

            <!-- Dashboard Content -->
            <div id="dashboardContent" class="hidden">
                <!-- Key Metrics -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="metric-card users">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-users text-2xl text-blue-500"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Aktive Brugere</p>
                                <p class="text-2xl font-semibold text-gray-900" id="activeUsers">-</p>
                                <p class="text-sm text-gray-500">af <span id="totalUsers">-</span> total</p>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card queries">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-search text-2xl text-green-500"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Søgninger</p>
                                <p class="text-2xl font-semibold text-gray-900" id="totalQueries">-</p>
                                <p class="text-sm text-gray-500"><span id="successRate">-</span>% succes</p>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card sessions">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-clock text-2xl text-purple-500"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Sessioner</p>
                                <p class="text-2xl font-semibold text-gray-900" id="totalSessions">-</p>
                                <p class="text-sm text-gray-500">Ø <span id="avgDuration">-</span> min</p>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card interactions">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-mouse-pointer text-2xl text-yellow-500"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Interaktioner</p>
                                <p class="text-2xl font-semibold text-gray-900" id="totalInteractions">-</p>
                                <p class="text-sm text-gray-500">Brugerengagement</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Popular Queries Chart -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                            Populære Søgninger
                        </h3>
                        <canvas id="popularQueriesChart" width="400" height="200"></canvas>
                    </div>

                    <!-- Category Breakdown Chart -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">
                            <i class="fas fa-chart-pie mr-2 text-green-600"></i>
                            Kategori Fordeling
                        </h3>
                        <canvas id="categoryChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Recent Activity & Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Recent Activity -->
                    <div class="lg:col-span-2 bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">
                                <i class="fas fa-history mr-2 text-purple-600"></i>
                                Seneste Aktivitet
                            </h3>
                        </div>
                        <div class="p-6">
                            <div id="recentActivity" class="space-y-4">
                                <!-- Dynamic content will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">
                                <i class="fas fa-bolt mr-2 text-yellow-600"></i>
                                Hurtige Handlinger
                            </h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <button onclick="navigateToUserSearch()" 
                                    class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-left">
                                <i class="fas fa-users mr-2"></i>
                                Søg Brugere
                            </button>
                            <button onclick="navigateToAnalytics()" 
                                    class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-left">
                                <i class="fas fa-chart-bar mr-2"></i>
                                Se Analytics
                            </button>
                            <button onclick="exportData()" 
                                    class="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-left">
                                <i class="fas fa-download mr-2"></i>
                                Eksporter Data
                            </button>
                            <button onclick="refreshDashboard()" 
                                    class="w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 text-left">
                                <i class="fas fa-sync-alt mr-2"></i>
                                Opdater Dashboard
                            </button>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="mt-8 bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-server mr-2 text-red-600"></i>
                            System Status
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-3 w-3 bg-green-400 rounded-full"></div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">API Status</p>
                                    <p class="text-sm text-gray-500">Operationel</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-3 w-3 bg-green-400 rounded-full"></div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">Database</p>
                                    <p class="text-sm text-gray-500">Forbundet</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-3 w-3 bg-yellow-400 rounded-full"></div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">Gennemsnitlig Responstid</p>
                                    <p class="text-sm text-gray-500" id="avgResponseTime">- ms</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * Admin Dashboard JavaScript
         *
         * Handles dashboard data loading, charts, and user interactions.
         */

        class AdminDashboard {
            constructor() {
                this.authToken = this.getAuthToken();
                this.charts = {};
                this.init();
            }

            init() {
                this.loadDashboardData();
                this.setupAutoRefresh();
            }

            getAuthToken() {
                return localStorage.getItem('admin_token') ||
                       this.getCookie('admin_token') ||
                       'your_admin_token_here';
            }

            getCookie(name) {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
            }

            async apiCall(endpoint, options = {}) {
                const defaultOptions = {
                    headers: {
                        'Authorization': `Bearer ${this.authToken}`,
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                };

                try {
                    const response = await fetch(endpoint, { ...defaultOptions, ...options });

                    if (!response.ok) {
                        if (response.status === 401) {
                            this.handleAuthError();
                            return null;
                        }
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return await response.json();
                } catch (error) {
                    console.error('API call failed:', error);
                    return null;
                }
            }

            handleAuthError() {
                alert('Din session er udløbet. Du bliver omdirigeret til login.');
                window.location.href = '/admin/login';
            }

            async loadDashboardData() {
                this.showLoading(true);

                try {
                    // Load system analytics
                    const analytics = await this.apiCall('/api/admin/analytics/system');

                    if (analytics) {
                        this.updateMetrics(analytics);
                        this.createCharts(analytics);
                        this.loadRecentActivity();
                    }
                } catch (error) {
                    console.error('Failed to load dashboard data:', error);
                } finally {
                    this.showLoading(false);
                }
            }

            showLoading(show) {
                const loadingState = document.getElementById('loadingState');
                const dashboardContent = document.getElementById('dashboardContent');

                if (show) {
                    loadingState.classList.remove('hidden');
                    dashboardContent.classList.add('hidden');
                } else {
                    loadingState.classList.add('hidden');
                    dashboardContent.classList.remove('hidden');
                }
            }

            updateMetrics(analytics) {
                // Update user metrics
                document.getElementById('activeUsers').textContent = analytics.users.active.toLocaleString('da-DK');
                document.getElementById('totalUsers').textContent = analytics.users.total.toLocaleString('da-DK');

                // Update query metrics
                document.getElementById('totalQueries').textContent = analytics.queries.total.toLocaleString('da-DK');
                document.getElementById('successRate').textContent = analytics.queries.success_rate.toFixed(1);

                // Update session metrics
                document.getElementById('totalSessions').textContent = analytics.sessions.total.toLocaleString('da-DK');
                document.getElementById('avgDuration').textContent = Math.round(analytics.sessions.avg_duration_seconds / 60);

                // Update interaction metrics
                document.getElementById('totalInteractions').textContent = analytics.interactions.total.toLocaleString('da-DK');

                // Update response time
                document.getElementById('avgResponseTime').textContent = analytics.queries.avg_response_time_ms;
            }

            createCharts(analytics) {
                this.createPopularQueriesChart(analytics.popular_queries);
                this.createCategoryChart(analytics.category_breakdown);
            }

            createPopularQueriesChart(popularQueries) {
                const ctx = document.getElementById('popularQueriesChart').getContext('2d');

                if (this.charts.popularQueries) {
                    this.charts.popularQueries.destroy();
                }

                this.charts.popularQueries = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: popularQueries.slice(0, 10).map(q => q.query.length > 15 ? q.query.substring(0, 15) + '...' : q.query),
                        datasets: [{
                            label: 'Antal søgninger',
                            data: popularQueries.slice(0, 10).map(q => q.count),
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            createCategoryChart(categoryBreakdown) {
                const ctx = document.getElementById('categoryChart').getContext('2d');

                if (this.charts.category) {
                    this.charts.category.destroy();
                }

                const colors = [
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(16, 185, 129, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(139, 92, 246, 0.8)'
                ];

                this.charts.category = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: categoryBreakdown.map(c => this.getCategoryLabel(c.category)),
                        datasets: [{
                            data: categoryBreakdown.map(c => c.count),
                            backgroundColor: colors.slice(0, categoryBreakdown.length),
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            getCategoryLabel(category) {
                const labels = {
                    'food_beverages': 'Mad & Drikke',
                    'household_cleaning': 'Husholdning',
                    'electronics': 'Elektronik',
                    'personal_care': 'Personlig pleje',
                    'home_garden': 'Hjem & Have'
                };
                return labels[category] || category;
            }

            async loadRecentActivity() {
                // This would load recent user activities, queries, etc.
                // For now, we'll show a placeholder
                const recentActivity = document.getElementById('recentActivity');
                recentActivity.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-clock text-2xl mb-2"></i>
                        <p>Seneste aktivitet vil blive vist her</p>
                        <p class="text-sm">Implementeres i næste version</p>
                    </div>
                `;
            }

            setupAutoRefresh() {
                // Refresh dashboard every 5 minutes
                setInterval(() => {
                    this.loadDashboardData();
                }, 5 * 60 * 1000);
            }
        }

        // Global functions
        function navigateToUserSearch() {
            window.location.href = '/admin/users';
        }

        function navigateToAnalytics() {
            window.location.href = '/admin/analytics';
        }

        function exportData() {
            window.location.href = '/admin/exports';
        }

        function refreshDashboard() {
            adminDashboard.loadDashboardData();
        }

        function logout() {
            localStorage.removeItem('admin_token');
            window.location.href = '/admin/login';
        }

        // Initialize dashboard
        let adminDashboard;
        document.addEventListener('DOMContentLoaded', () => {
            adminDashboard = new AdminDashboard();
        });
    </script>
</body>
</html>
