/**
 * Admin User Search Interface
 * 
 * JavaScript functionality for the admin user search and profile interface.
 * Handles API calls, data display, and user interactions.
 */

class AdminUserSearch {
    constructor() {
        this.currentPage = 0;
        this.pageSize = 20;
        this.totalResults = 0;
        this.currentFilters = {};
        this.authToken = this.getAuthToken();
        
        // Initialize the interface
        this.init();
    }
    
    init() {
        // Set default date range (last 30 days)
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
        
        document.getElementById('dateTo').value = today.toISOString().split('T')[0];
        document.getElementById('dateFrom').value = thirtyDaysAgo.toISOString().split('T')[0];
        
        // Add event listeners
        this.setupEventListeners();
        
        // Load initial data
        this.searchUsers();
    }
    
    setupEventListeners() {
        // Search on Enter key
        document.getElementById('searchTerm').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchUsers();
            }
        });
        
        // Auto-search on filter changes (with debounce)
        const filters = ['userType', 'dateFrom', 'dateTo'];
        filters.forEach(filterId => {
            document.getElementById(filterId).addEventListener('change', () => {
                this.debounce(() => this.searchUsers(), 500)();
            });
        });
    }
    
    getAuthToken() {
        // Get JWT token from localStorage or cookie
        return localStorage.getItem('admin_token') || 
               this.getCookie('admin_token') || 
               'your_admin_token_here'; // Replace with actual token
    }
    
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    async apiCall(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Authorization': `Bearer ${this.authToken}`,
                'Content-Type': 'application/json',
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(endpoint, { ...defaultOptions, ...options });
            
            if (!response.ok) {
                if (response.status === 401) {
                    this.handleAuthError();
                    return null;
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            this.showError(`API fejl: ${error.message}`);
            return null;
        }
    }
    
    handleAuthError() {
        alert('Din session er udløbet. Du bliver omdirigeret til login.');
        // Redirect to login page
        window.location.href = '/admin/login';
    }
    
    showError(message) {
        // Create or update error message
        let errorDiv = document.getElementById('errorMessage');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.id = 'errorMessage';
            errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
            document.querySelector('.max-w-7xl').insertBefore(errorDiv, document.querySelector('.bg-white'));
        }
        
        errorDiv.innerHTML = `
            <div class="flex justify-between items-center">
                <span><i class="fas fa-exclamation-triangle mr-2"></i>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="text-red-700 hover:text-red-900">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }
    
    async searchUsers() {
        this.showLoading(true);
        
        // Collect filter values
        const filters = {
            search_term: document.getElementById('searchTerm').value.trim() || undefined,
            user_type: document.getElementById('userType').value || undefined,
            date_from: document.getElementById('dateFrom').value ? 
                      new Date(document.getElementById('dateFrom').value).toISOString() : undefined,
            date_to: document.getElementById('dateTo').value ? 
                    new Date(document.getElementById('dateTo').value).toISOString() : undefined,
            limit: this.pageSize,
            offset: this.currentPage * this.pageSize
        };
        
        // Remove undefined values
        Object.keys(filters).forEach(key => {
            if (filters[key] === undefined) {
                delete filters[key];
            }
        });
        
        this.currentFilters = filters;
        
        // Build query string
        const queryString = new URLSearchParams(filters).toString();
        const endpoint = `/api/admin/users/search?${queryString}`;
        
        const data = await this.apiCall(endpoint);
        
        this.showLoading(false);
        
        if (data) {
            this.displayResults(data);
        }
    }
    
    showLoading(show) {
        const loadingState = document.getElementById('loadingState');
        const emptyState = document.getElementById('emptyState');
        const resultsTable = document.getElementById('resultsTable');
        
        if (show) {
            loadingState.classList.remove('hidden');
            emptyState.classList.add('hidden');
            resultsTable.classList.add('hidden');
        } else {
            loadingState.classList.add('hidden');
        }
    }
    
    displayResults(data) {
        const { users, total_count, limit, offset, has_more } = data;
        
        this.totalResults = total_count;
        
        const emptyState = document.getElementById('emptyState');
        const resultsTable = document.getElementById('resultsTable');
        const resultsCount = document.getElementById('resultsCount');
        
        if (users.length === 0) {
            emptyState.classList.remove('hidden');
            resultsTable.classList.add('hidden');
            resultsCount.textContent = 'Ingen resultater';
        } else {
            emptyState.classList.add('hidden');
            resultsTable.classList.remove('hidden');
            resultsCount.textContent = `${total_count} brugere fundet`;
            
            this.renderUserTable(users);
            this.updatePagination(offset, limit, total_count, has_more);
        }
    }
    
    renderUserTable(users) {
        const tbody = document.getElementById('userTableBody');
        tbody.innerHTML = '';
        
        users.forEach(user => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            
            const lastActivity = user.last_activity ? 
                new Date(user.last_activity).toLocaleDateString('da-DK') : 'Aldrig';
            
            const avgDuration = user.avg_session_duration ? 
                this.formatDuration(user.avg_session_duration) : '0 min';
            
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <i class="fas fa-user text-gray-600"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${user.username}</div>
                            <div class="text-sm text-gray-500">ID: ${user.id}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="user-type-badge ${user.user_type}">${this.getUserTypeLabel(user.user_type)}</span>
                    ${user.is_admin ? '<span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Admin</span>' : ''}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>${user.total_queries} søgninger</div>
                    <div class="text-gray-500">${user.total_sessions} sessioner</div>
                    <div class="text-gray-500">Ø ${avgDuration}/session</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${lastActivity}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="adminUserSearch.viewUserProfile(${user.id})" 
                            class="text-blue-600 hover:text-blue-900 mr-3">
                        <i class="fas fa-eye mr-1"></i>
                        Se profil
                    </button>
                    <button onclick="adminUserSearch.viewUserTimeline(${user.id})" 
                            class="text-green-600 hover:text-green-900">
                        <i class="fas fa-history mr-1"></i>
                        Tidslinje
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    }
    
    getUserTypeLabel(userType) {
        const labels = {
            'power_user': 'Power Bruger',
            'regular_user': 'Almindelig',
            'casual_user': 'Lejlighedsbruger',
            'inactive_regular': 'Inaktiv',
            'inactive_casual': 'Inaktiv',
            'new_user': 'Ny Bruger'
        };
        return labels[userType] || userType;
    }
    
    formatDuration(seconds) {
        if (seconds < 60) return `${seconds}s`;
        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) return `${minutes} min`;
        const hours = Math.floor(minutes / 60);
        return `${hours}t ${minutes % 60}m`;
    }
    
    updatePagination(offset, limit, totalCount, hasMore) {
        const showingFrom = offset + 1;
        const showingTo = Math.min(offset + limit, totalCount);
        
        document.getElementById('showingFrom').textContent = showingFrom;
        document.getElementById('showingTo').textContent = showingTo;
        document.getElementById('totalResults').textContent = totalCount;
        
        const prevButton = document.getElementById('prevPage');
        const nextButton = document.getElementById('nextPage');
        
        prevButton.disabled = offset === 0;
        nextButton.disabled = !hasMore;
        
        if (prevButton.disabled) {
            prevButton.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            prevButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
        
        if (nextButton.disabled) {
            nextButton.classList.add('opacity-50', 'cursor-not-allowed');
        } else {
            nextButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
    
    async viewUserProfile(userId) {
        const data = await this.apiCall(`/api/admin/users/${userId}/profile`);
        
        if (data) {
            this.showUserProfileModal(data);
        }
    }
    
    showUserProfileModal(userData) {
        const modal = document.getElementById('userProfileModal');
        const content = document.getElementById('userProfileContent');
        
        content.innerHTML = this.renderUserProfile(userData);
        modal.classList.remove('hidden');
        
        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeUserProfile();
            }
        });
    }
    
    renderUserProfile(user) {
        const createdAt = new Date(user.created_at).toLocaleDateString('da-DK');
        const lastActivity = user.last_activity ? 
            new Date(user.last_activity).toLocaleDateString('da-DK') : 'Aldrig';
        
        return `
            <div class="space-y-6">
                <!-- User Header -->
                <div class="flex items-center space-x-4">
                    <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                        <i class="fas fa-user text-2xl text-gray-600"></i>
                    </div>
                    <div>
                        <h4 class="text-xl font-semibold text-gray-900">${user.username}</h4>
                        <p class="text-gray-500">Bruger ID: ${user.id}</p>
                        <div class="mt-1">
                            <span class="user-type-badge ${user.user_type}">${this.getUserTypeLabel(user.user_type)}</span>
                            ${user.is_admin ? '<span class="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Admin</span>' : ''}
                        </div>
                    </div>
                </div>
                
                <!-- Stats Grid -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">${user.stats.total_queries}</div>
                        <div class="text-sm text-gray-600">Søgninger</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">${user.stats.total_sessions}</div>
                        <div class="text-sm text-gray-600">Sessioner</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">${user.stats.total_interactions}</div>
                        <div class="text-sm text-gray-600">Interaktioner</div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-yellow-600">${this.formatDuration(user.stats.avg_session_duration)}</div>
                        <div class="text-sm text-gray-600">Ø Session</div>
                    </div>
                </div>
                
                <!-- Activity Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Query Categories -->
                    <div>
                        <h5 class="text-lg font-medium text-gray-900 mb-3">Søgekategorier</h5>
                        <div class="space-y-2">
                            ${Object.entries(user.query_categories || {}).map(([category, count]) => `
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">${this.getCategoryLabel(category)}</span>
                                    <span class="text-sm font-medium text-gray-900">${count}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <!-- Interaction Types -->
                    <div>
                        <h5 class="text-lg font-medium text-gray-900 mb-3">Interaktionstyper</h5>
                        <div class="space-y-2">
                            ${Object.entries(user.interaction_types || {}).map(([type, count]) => `
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">${this.getInteractionLabel(type)}</span>
                                    <span class="text-sm font-medium text-gray-900">${count}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex space-x-3 pt-4 border-t">
                    <button onclick="adminUserSearch.viewUserTimeline(${user.id})" 
                            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        <i class="fas fa-history mr-2"></i>
                        Se tidslinje
                    </button>
                    <button onclick="adminUserSearch.viewUserQueries(${user.id})" 
                            class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        <i class="fas fa-search mr-2"></i>
                        Se søgninger
                    </button>
                </div>
            </div>
        `;
    }
    
    getCategoryLabel(category) {
        const labels = {
            'food_beverages': 'Mad & Drikke',
            'household_cleaning': 'Husholdning',
            'electronics': 'Elektronik',
            'personal_care': 'Personlig pleje',
            'home_garden': 'Hjem & Have'
        };
        return labels[category] || category;
    }
    
    getInteractionLabel(type) {
        const labels = {
            'product_clicked': 'Produktklik',
            'catalog_viewed': 'Katalogvisning',
            'store_selected': 'Butikvalg',
            'query_refined': 'Søgeforbedring'
        };
        return labels[type] || type;
    }
    
    closeUserProfile() {
        document.getElementById('userProfileModal').classList.add('hidden');
    }
    
    viewUserTimeline(userId) {
        // Navigate to timeline view (will be implemented in next task)
        window.location.href = `/admin/users/${userId}/timeline`;
    }
    
    viewUserQueries(userId) {
        // Navigate to queries view (will be implemented in next task)
        window.location.href = `/admin/users/${userId}/queries`;
    }
    
    clearFilters() {
        document.getElementById('searchTerm').value = '';
        document.getElementById('userType').value = '';
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        
        this.currentPage = 0;
        this.searchUsers();
    }
    
    previousPage() {
        if (this.currentPage > 0) {
            this.currentPage--;
            this.searchUsers();
        }
    }
    
    nextPage() {
        this.currentPage++;
        this.searchUsers();
    }
}

// Global functions for HTML onclick handlers
let adminUserSearch;

function searchUsers() {
    adminUserSearch.searchUsers();
}

function clearFilters() {
    adminUserSearch.clearFilters();
}

function previousPage() {
    adminUserSearch.previousPage();
}

function nextPage() {
    adminUserSearch.nextPage();
}

function closeUserProfile() {
    adminUserSearch.closeUserProfile();
}

function logout() {
    localStorage.removeItem('admin_token');
    window.location.href = '/admin/login';
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    adminUserSearch = new AdminUserSearch();
});
